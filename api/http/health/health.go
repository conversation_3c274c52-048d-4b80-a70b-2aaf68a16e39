package health

import "time"

const (
	StatusUp      = "UP"
	StatusDown    = "DOWN"
	StatusUnknown = "UNKNOWN"
)

var (
	HealthOK = HealthResp{
		Status:    StatusUp,
		Timestamp: time.Now().Unix(),
		Details:   make(map[string]any),
	}
)

type HealthResp struct {
	Status    string         `json:"status"`
	Timestamp int64          `json:"timestamp"`
	Details   map[string]any `json:"details"`
	Error     string         `json:"error,omitempty"`
}
