package health

import (
	"context"
	"fmt"
	"net/http"
	"time"

	restfulspec "github.com/emicklei/go-restful-openapi"
	"github.com/emicklei/go-restful/v3"

	service "transwarp.io/mlops/pipeline/api/http/service"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"

	client "transwarp.io/mlops/pipeline/internal/infrastructure/client"
)

type ClusterService struct {
	ServiceName string
	rootPath    string
}

func init() {
	s := &ClusterService{
		ServiceName: "ClusterService",
		rootPath:    fmt.Sprintf("%s", service.V3()),
	}
	service.RegisterService(s)
}

func (s *ClusterService) Name() string {
	return s.ServiceName
}

func (s *ClusterService) NewWebService() *restful.WebService {
	tags := []string{"Cluster"}
	metaK, metaV := restfulspec.KeyOpenAPITags, tags
	ws := new(restful.WebService)
	ws.Path(s.rootPath).
		Consumes(restful.MIME_JSON, restful.MIME_OCTET).
		Produces(restful.MIME_JSON, restful.MIME_OCTET)

	ws.Route(ws.GET("/cluster/status").To(s.GetServiceStatus).
		Doc("Cluster status, include K8s cluster information including Kueue, LeaderWorkerSet, and K8s version").
		Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), Cluster{}))

	return ws
}

func successResponse(response *restful.Response, value interface{}) {
	err := response.WriteHeaderAndEntity(http.StatusOK, value)
	if err != nil {
		zlog.SugarErrorf("restful: error while writing header and entity")
	}
}

func (s *ClusterService) GetServiceStatus(req *restful.Request, resp *restful.Response) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	cluster := &Cluster{
		Status:     StatusUp,
		Components: make([]Component, 0),
	}

	k8sVersion, err := s.getKubernetesVersion(ctx)
	if err != nil {
		zlog.SugarErrorf("Failed to get Kubernetes version: %v", err)
		cluster.Status = StatusDown
		cluster.Message = err.Error()
	} else {
		cluster.Version = k8sVersion
	}

	s.checkComponents(ctx, cluster)

	successResponse(resp, cluster)
}

func (s *ClusterService) getKubernetesVersion(ctx context.Context) (string, error) {
	k8sClient := client.MustGetK8sClient()
	discoveryClient := k8sClient.Clientset.DiscoveryClient

	version, err := discoveryClient.ServerVersion()
	if err != nil {
		return "", fmt.Errorf("failed to get server version: %w", err)
	}

	return version.GitVersion, nil
}

func (s *ClusterService) checkComponents(ctx context.Context, cluster *Cluster) {
	cluster.Components = append(cluster.Components, s.checkKueue(ctx))
	cluster.Components = append(cluster.Components, s.checkLeaderWorkerSet(ctx))
	cluster.Components = append(cluster.Components, s.checkArgoWorkflows(ctx))
	cluster.Components = append(cluster.Components, s.checkJobSet(ctx))
	cluster.Components = append(cluster.Components, s.checkAppWrapper(ctx))
	cluster.Components = append(cluster.Components, s.checkRay(ctx))
}

func (s *ClusterService) checkKueue(ctx context.Context) Component {
	info := Component{
		Name:    ComponentKueue,
		Status:  StatusUp,
		Enabled: true,
	}

	if !client.MustGetKueueClient().IsKueueSupported() {
		info.Enabled = false
		info.Status = StatusDown
		info.Message = "Kueue CRDs not found"
		return info
	}

	defer func() {
		if r := recover(); r != nil {
			info.Status = StatusDown
			info.Message = fmt.Sprintf("Kueue client initialization failed: %v", r)
		}
	}()

	kueueClient := client.MustGetKueueClient()
	if kueueClient == nil {
		info.Status = StatusDown
		info.Message = "Failed to initialize Kueue client"
		return info
	}

	workloads, err := kueueClient.ListWorkloads("")
	if err != nil {
		info.Status = StatusDown
		info.Message = fmt.Sprintf("Failed to list workloads: %v", err)
	} else {
		info.Message = fmt.Sprintf("Found %d workloads", len(workloads))
	}

	return info
}

func (s *ClusterService) checkLeaderWorkerSet(ctx context.Context) Component {
	info := Component{
		Name:    ComponentLeaderWorkerSet,
		Status:  StatusUp,
		Enabled: true,
	}

	if !client.MustGetLeaderWorkerSetClient().IsLWSSupported() {
		info.Enabled = false
		info.Status = StatusDown
		info.Message = "LeaderWorkerSet CRDs not found"
		return info
	}

	defer func() {
		if r := recover(); r != nil {
			info.Status = StatusDown
			info.Message = fmt.Sprintf("LeaderWorkerSet client initialization failed: %v", r)
		}
	}()

	lwsClient := client.MustGetLeaderWorkerSetClient()
	if lwsClient == nil {
		info.Status = StatusDown
		info.Message = "Failed to initialize LeaderWorkerSet client"
		return info
	}

	info.Message = "LeaderWorkerSet is available"
	return info
}

func (s *ClusterService) checkArgoWorkflows(ctx context.Context) Component {
	info := Component{
		Name:    ComponentArgoWorkflows,
		Status:  StatusUp,
		Enabled: true,
	}

	if !client.MustGetArgoWorkflowClient().IsArgoWorkflowSupported() {
		info.Enabled = false
		info.Status = StatusDown
		info.Message = "Argo Workflows CRDs not found"
		return info
	}

	info.Enabled = true
	info.Message = "Argo Workflows CRDs are available"
	return info
}

func (s *ClusterService) checkJobSet(ctx context.Context) Component {
	info := Component{
		Name:    ComponentJobSet,
		Status:  StatusUp,
		Enabled: true,
	}

	if !client.MustGetJobSetClient().IsJobSetSupported() {
		info.Enabled = false
		info.Status = StatusDown
		info.Message = "JobSet CRDs not found"
		return info
	}

	info.Message = "JobSet CRDs are available"
	return info
}

func (s *ClusterService) checkAppWrapper(ctx context.Context) Component {
	info := Component{
		Name:    ComponentAppWrapper,
		Status:  StatusUp,
		Enabled: true,
	}

	if !client.MustGetAppWrapperClient().IsAppWrapperSupported() {
		info.Enabled = false
		info.Status = StatusDown
		info.Message = "AppWrapper CRDs not found"
		return info
	}

	info.Message = "AppWrapper CRDs are available"
	return info
}

func (s *ClusterService) checkRay(ctx context.Context) Component {
	info := Component{
		Name:    ComponentRay,
		Status:  StatusUp,
		Enabled: true,
	}

	if !client.MustGetRayClient().IsRaySupported() {
		info.Enabled = false
		info.Status = StatusDown
		info.Message = "Ray CRDs not found"
		return info
	}

	info.Message = "Ray CRDs are available"
	return info
}
