package health

const (
	StatusUp      = "UP"
	StatusDown    = "DOWN"
	StatusUnknown = "UNKNOWN"
)

type Cluster struct {
	Version    string      `json:"version"`
	Status     string      `json:"status"`
	Timestamp  int64       `json:"timestamp"`
	Components []Component `json:"components"`

	Message string `json:"message"`
}

type ComponentName string

const (
	ComponentKueue           ComponentName = "kueue"
	ComponentLeaderWorkerSet ComponentName = "leader_worker_set"
	ComponentArgoWorkflows   ComponentName = "argo_workflows"
	ComponentJobSet          ComponentName = "jobset"
	ComponentAppWrapper      ComponentName = "appwrapper"
	ComponentRay             ComponentName = "ray"
)

type Component struct {
	Name ComponentName `json:"name"`

	Enabled bool   `json:"enabled"`
	Version string `json:"version,omitempty"`
	Status  string `json:"status"`
	Message string `json:"message,omitempty"`
}
