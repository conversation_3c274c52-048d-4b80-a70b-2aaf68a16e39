package http

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/emicklei/go-restful/v3"
	token "transwarp.io/mlops/mlops-std/token"
	service "transwarp.io/mlops/pipeline/api/http/service"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"

	_ "transwarp.io/mlops/pipeline/api/http/cluster"
	_ "transwarp.io/mlops/pipeline/api/http/health"
	_ "transwarp.io/mlops/pipeline/api/http/pipeline"
	_ "transwarp.io/mlops/pipeline/api/http/swagger"
	_ "transwarp.io/mlops/pipeline/api/http/task"
)

type HTTPServer struct {
	container *restful.Container
	opts      *Option
}

func NewHTTPServer(opts *Option) *HTTPServer {
	opts = completedOptions(opts)
	return &HTTPServer{
		container: restful.DefaultContainer,
		opts:      opts,
	}
}

func (s *HTTPServer) Start(ctx context.Context) chan error {

	service.InitService(s.container)

	if s.opts.EnableSwagger {
		s.configureSwagger()
	}

	for _, ws := range restful.RegisteredWebServices() {
		ws.Filter(NCSACommonLogFormatLogger).Filter(Authenticate)
	}

	server := &http.Server{
		Addr:           fmt.Sprintf(":%d", s.opts.Port),
		Handler:        s.container,
		ReadTimeout:    s.opts.ReadTimeout,
		WriteTimeout:   s.opts.WriteTimeout,
		MaxHeaderBytes: s.opts.MaxHeaderBytes,
	}

	go func() {
		<-ctx.Done()
		shutdownCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		if err := server.Shutdown(shutdownCtx); err != nil {
			zlog.SugarErrorf("Failed to shutdown HTTP server: %v", err)
		}
	}()

	err := make(chan error)
	go func() {
		zlog.SugarInfof("Starting HTTP server on port %d", s.opts.Port)
		if e := server.ListenAndServe(); e != nil && e != http.ErrServerClosed {
			zlog.SugarFatalf("HTTP server ListenAndServe: %v", e)
			err <- e
		}
	}()

	return err
}

func (s *HTTPServer) configureSwagger() {
}

type Option struct {
	Port           int
	ReadTimeout    time.Duration
	WriteTimeout   time.Duration
	MaxHeaderBytes int
	EnableSwagger  bool
}

func completedOptions(opts *Option) *Option {
	if opts == nil {
		opts = &Option{}
	}

	if opts.Port == 0 {
		opts.Port = 80
	}

	if opts.ReadTimeout == 0 {
		opts.ReadTimeout = 60 * time.Second
	}

	if opts.WriteTimeout == 0 {
		opts.WriteTimeout = 60 * time.Second
	}

	if opts.MaxHeaderBytes == 0 {
		opts.MaxHeaderBytes = 1 << 32 // MB
	}

	return opts
}

// if not token supplied, return 401 (only check api path startwith "/api")
func Authenticate(req *restful.Request, resp *restful.Response, chain *restful.FilterChain) {
	t := req.Request.Header.Get(token.AUTH_HEADER)
	path := req.Request.URL.Path
	if strings.HasPrefix(path, service.V1()) && "" == t {
		zlog.SugarErrorf("no token supplied when request path: %s", path)
		resp.WriteErrorString(401, "401: Not Authorized")
		return
	}
	chain.ProcessFilter(req, resp)
}

var NCSACommonLogFormatLogger = func(req *restful.Request, resp *restful.Response, chain *restful.FilterChain) {
	username := "-"
	if req.Request.URL.User != nil {
		if name := req.Request.URL.User.Username(); name != "" {
			username = name
		}
	}

	now := time.Now()
	// TODO, 适配tdc的ingress跳转
	spaBasePath := req.HeaderParameter("/spa")
	if spaBasePath != "" {
		cookie := http.Cookie{
			Name:  "basename",
			Value: spaBasePath,
			Path:  "/",
		}
		resp.AddHeader("Set-Cookie", cookie.String())
	}
	chain.ProcessFilter(req, resp)
	if req.Request.URL.RequestURI() == "/actuator/health" && resp.StatusCode() == 200 {
		return
	}
	zlog.SugarInfof("%s %s %s %s %s %d %d %v",
		strings.Split(req.Request.RemoteAddr, ":")[0],
		username,
		req.Request.Method,
		req.Request.URL.RequestURI(),
		req.Request.Proto,
		resp.StatusCode(),
		resp.ContentLength(),
		time.Now().Sub(now),
	)
}
