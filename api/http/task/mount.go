package task

import (
	restfulspec "github.com/emicklei/go-restful-openapi/v2"
	restful "github.com/emicklei/go-restful/v3"
	"net/http"

	"transwarp.io/mlops/pipeline/api/http/service"
	"transwarp.io/mlops/pipeline/internal/core/manager/task"
	model "transwarp.io/mlops/pipeline/internal/core/model/task"
)

type TaskService struct {
	ServiceName string
	rootPath    string
	tm          *task.TaskManager
}

func init() {
	s := &TaskService{
		ServiceName: "TaskService",
		rootPath:    service.V3Task(),

		tm: task.NewTaskManager(),
	}
	service.RegisterService(s)
}

func (r *TaskService) Name() string {
	return r.ServiceName
}

func (r *TaskService) NewWebService() *restful.WebService {
	tags := []string{"Task"}
	metaK, metaV := restfulspec.KeyOpenAPITags, tags
	ws := new(restful.WebService)
	ws.Path(r.rootPath).
		Consumes(restful.MIME_JSON, restful.MIME_OCTET).
		Produces(restful.MIME_JSON, restful.MIME_OCTET)

	ws.Route(ws.POST("").
		To(r.CreateTask).
		Doc("create task from YAML").
		Metadata(metaK, metaV).
		Consumes("application/x-yaml", "text/yaml").
		Returns(http.StatusOK, "OK", model.Task{}))
	ws.Route(ws.DELETE("/{id}").
		To(r.DeleteTask).
		Doc("delete task by id").
		Metadata(metaK, metaV).
		Param(ws.PathParameter("id", "task id").DataType("string")).
		Returns(http.StatusOK, "OK", model.Task{}))
	ws.Route(ws.GET("").
		To(r.ListTasks).
		Doc("list task").
		Metadata(metaK, metaV).
		Param(ws.QueryParameter("page", "page").DataType("integer").DefaultValue("1")).
		Param(ws.QueryParameter("page_size", "page size").DataType("integer").DefaultValue("20")).
		Param(ws.QueryParameter("project_id", "project id").DataType("string")).
		Param(ws.QueryParameter("tenant_id", "tenant id").DataType("string")).
		Param(ws.QueryParameter("status", "status").DataType("string")).
		Param(ws.QueryParameter("type", "type").DataType("string")).
		Param(ws.QueryParameter("priority", "priority").DataType("integer")).
		Param(ws.QueryParameter("creator", "creator").DataType("string")).
		Param(ws.QueryParameter("queue_name", "queue name").DataType("string").DefaultValue("task")).
		Param(ws.QueryParameter("search_keyword", "search keyword").DataType("string")).
		Param(ws.QueryParameter("sort_by", "sort by filed").DataType("string").DefaultValue("created_at")).
		Param(ws.QueryParameter("sort_order", "sort order, desc or asc").DataType("string").DefaultValue("desc")).
		Param(ws.QueryParameter("created_at_start_time", "created at start time").DataType("integer")).
		Param(ws.QueryParameter("created_at_end_time", "created at end time").DataType("integer")).
		Returns(http.StatusOK, "OK", model.ListTasksResp{}))
	ws.Route(ws.GET("/{id}").
		To(r.GetTask).
		Doc("get task by id").
		Metadata(metaK, metaV).
		Param(ws.PathParameter("id", "task id").DataType("string")).
		Returns(http.StatusOK, "OK", model.Task{}))
	ws.Route(ws.POST("/{id}/rerun").
		To(r.RerunTask).
		Doc("rerun task by id").
		Metadata(metaK, metaV).
		Param(ws.PathParameter("id", "task id").DataType("string")).
		Returns(http.StatusOK, "OK", struct{}{}))
	ws.Route(ws.POST("/{id}/cancel").
		To(r.CancelTask).
		Doc("cancel task by id").
		Metadata(metaK, metaV).
		Param(ws.PathParameter("id", "task id").DataType("string")).
		Returns(http.StatusOK, "OK", struct{}{}))
	ws.Route(ws.POST("/{id}/stop").
		To(r.StopTask).
		Doc("stop task by id, meaning stop all running pods, but keep the task in the queue").
		Metadata(metaK, metaV).
		Param(ws.PathParameter("id", "task id").DataType("string")).
		Returns(http.StatusOK, "OK", struct{}{}))
	ws.Route(ws.POST("/{id}/resume").
		To(r.ResumeTask).
		Doc("resume task by id, only stoped task can call resume").
		Metadata(metaK, metaV).
		Param(ws.PathParameter("id", "task id").DataType("string")).
		Returns(http.StatusOK, "OK", struct{}{}))

	// task priority
	ws.Route(ws.GET("/priorities").
		To(r.ListTaskPriorities).
		Doc("list all task priorities").
		Metadata(metaK, metaV).
		Returns(200, "OK", ListPrioritiesResp{}))
	// task type
	ws.Route(ws.GET("/types").
		To(r.ListTaskTypes).
		Doc("list all task types").
		Metadata(metaK, metaV).
		Returns(200, "OK", ListTypesResp{}))
	ws.Route(ws.GET("/statuses").
		To(r.ListTaskStatuses).
		Doc("list all task status").
		Metadata(metaK, metaV).
		Returns(200, "OK", ListStatusesResp{}))
	ws.Route(ws.GET("/queues").
		To(r.ListQueues).
		Doc("list all queues").
		Metadata(metaK, metaV).
		Returns(200, "OK", ListStatusesResp{}))

	return ws
}
