package task

import (
	"context"
	"io"
	"net/http"
	"strconv"
	"strings"

	"github.com/emicklei/go-restful/v3"

	"transwarp.io/mlops/pipeline/api/http/helper"
	model "transwarp.io/mlops/pipeline/internal/core/model/task"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

func (this *TaskService) CreateTask(request *restful.Request, response *restful.Response) {
	ctx := request.Request.Context()

	body, err := io.ReadAll(request.Request.Body)
	if err != nil {
		zlog.SugarErrorf("Failed to read request body: %v", err)
		response.WriteErrorString(http.StatusBadRequest, "Failed to read request body: "+err.Error())
		return
	}

	if len(body) == 0 {
		response.WriteErrorString(http.StatusBadRequest, "Request body cannot be empty")
		return
	}

	task, err := this.tm.CreateTaskFromYAML(ctx, string(body))
	if err != nil {
		zlog.SugarErrorf("Failed to create task: %v", err)
		response.WriteErrorString(http.StatusInternalServerError, "Failed to create task: "+err.Error())
		return
	}

	response.WriteEntity(task)
}

func (this *TaskService) DeleteTask(request *restful.Request, response *restful.Response) {
	id := request.PathParameter("id")
	ctx := context.Background()
	if err := this.tm.DeleteTask(ctx, id); err != nil {
		response.WriteErrorString(http.StatusInternalServerError, "Failed to delete task: "+err.Error())
	}
	response.Flush()
}

func (this *TaskService) ListTasks(request *restful.Request, response *restful.Response) {
	req := this.parseListRequest(request)

	result, err := this.tm.ListTasks(request.Request.Context(), req)
	if err != nil {
		zlog.SugarErrorf("Failed to list tasks: %w", err)
		response.WriteErrorString(http.StatusInternalServerError, "Failed to list tasks: "+err.Error())
		return
	}

	response.WriteEntity(result)
}

func (this *TaskService) GetTask(request *restful.Request, response *restful.Response) {
	id := request.PathParameter("id")

	if id == "" {
		response.WriteErrorString(http.StatusBadRequest, "task id are required")
		return
	}

	task, err := this.tm.GetTask(request.Request.Context(), id)
	if err != nil {
		zlog.SugarErrorf("Failed to get task %s, error: %w", id, err)
		response.WriteErrorString(http.StatusInternalServerError, "Failed to get task: "+err.Error())
		return
	}

	response.WriteEntity(task)
}

func (this *TaskService) RerunTask(request *restful.Request, response *restful.Response) {
	id := request.PathParameter("id")
	ctx := request.Request.Context()

	if id == "" {
		response.WriteErrorString(http.StatusBadRequest, "task id is required")
		return
	}

	task, err := this.tm.RerunTask(ctx, id)
	if err != nil {
		zlog.SugarErrorf("Failed to rerun task %s: %v", id, err)
		response.WriteErrorString(http.StatusInternalServerError, "Failed to rerun task: "+err.Error())
		return
	}
	response.WriteEntity(task)
}

func (this *TaskService) CancelTask(request *restful.Request, response *restful.Response) {
	id := request.PathParameter("id")
	ctx := request.Request.Context()

	if id == "" {
		response.WriteErrorString(http.StatusBadRequest, "task id is required")
		return
	}

	err := this.tm.CancelTask(ctx, id)
	if err != nil {
		zlog.SugarErrorf("Failed to cancel task %s: %v", id, err)
		response.WriteErrorString(http.StatusInternalServerError, "Failed to cancel task: "+err.Error())
		return
	}

	response.WriteEntity(struct{}{})
}

func (this *TaskService) StopTask(request *restful.Request, response *restful.Response) {
	id := request.PathParameter("id")
	ctx := request.Request.Context()

	if id == "" {
		response.WriteErrorString(http.StatusBadRequest, "task id is required")
		return
	}

	err := this.tm.StopTask(ctx, id)
	if err != nil {
		zlog.SugarErrorf("Failed to stop task %s: %v", id, err)
		response.WriteErrorString(http.StatusInternalServerError, "Failed to stop task: "+err.Error())
		return
	}

	response.WriteEntity(struct{}{})
}

func (this *TaskService) ResumeTask(request *restful.Request, response *restful.Response) {
	id := request.PathParameter("id")
	ctx := request.Request.Context()

	if id == "" {
		response.WriteErrorString(http.StatusBadRequest, "task id is required")
		return
	}

	err := this.tm.ResumeTask(ctx, id)
	if err != nil {
		zlog.SugarErrorf("Failed to resume task %s: %v", id, err)
		response.WriteErrorString(http.StatusInternalServerError, "Failed to resume task: "+err.Error())
		return
	}

	response.WriteEntity(struct{}{})
}

func (this *TaskService) parseListRequest(request *restful.Request) *model.ListTasksReq {
	req := &model.ListTasksReq{
		Page:      1,
		PageSize:  20,
		QueueName: "task",
		SortBy:    "create_at",
		SortOrder: "desc",
	}

	if page := request.QueryParameter("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			req.Page = int32(p)
		}
	}

	if pageSize := request.QueryParameter("page_size"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil && ps > 0 {
			req.PageSize = int32(ps)
		}
	}

	req.SearchKeyword = request.QueryParameter("search_keyword")
	req.SortBy = request.QueryParameter("sort_by")
	req.SortOrder = request.QueryParameter("sort_order")

	if tenantIDsArr := request.QueryParameters("tenant_id"); len(tenantIDsArr) > 0 {
		for _, id := range tenantIDsArr {
			ids := strings.Split(id, ",")
			req.TenantIDs = append(req.TenantIDs, ids...)
		}
	}

	if statusArr := request.QueryParameters("status"); len(statusArr) > 0 {
		for _, s := range statusArr {
			status := strings.Split(s, ",")
			req.Status = append(req.Status, status...)
		}
	}

	if projectIDsArr := request.QueryParameters("project_id"); len(projectIDsArr) > 0 {
		for _, id := range projectIDsArr {
			ids := strings.Split(id, ",")
			req.ProjectIDs = append(req.ProjectIDs, ids...)
		}
	}

	if types := request.QueryParameters("type"); len(types) > 0 {
		req.Types = types
	}

	if prioritiesArr := request.QueryParameters("priority"); len(prioritiesArr) > 0 {
		for _, p := range prioritiesArr {
			priorities := strings.Split(p, ",")
			for _, p := range priorities {
				if priority, err := strconv.Atoi(p); err == nil {
					req.Priorities = append(req.Priorities, int32(priority))
				}
			}
		}
	}

	if createdAtStart := request.QueryParameter("created_at_start"); createdAtStart != "" {
		if ts, err := strconv.ParseInt(createdAtStart, 10, 64); err == nil {
			req.CreatedAtStart = &ts
		}
	}

	if createdAtEnd := request.QueryParameter("created_at_end"); createdAtEnd != "" {
		if ts, err := strconv.ParseInt(createdAtEnd, 10, 64); err == nil {
			req.CreatedAtEnd = &ts
		}
	}

	if queueName := request.QueryParameter("queue_name"); queueName != "" {
		req.QueueName = queueName
	}

	return req
}

func (this *TaskService) ListTaskTypes(request *restful.Request, response *restful.Response) {
	lang := helper.GetLanguageFromRequest(request.Request)

	resp := &ListTypesResp{
		Items: getEnumItems(lang, typeData),
	}

	if err := response.WriteEntity(resp); err != nil {
		zlog.SugarErrorf("Error writing response for ListTaskTypes: %v", err)
		response.WriteErrorString(http.StatusInternalServerError, "Failed to write response for task types")
	}
}

func (this *TaskService) ListTaskPriorities(request *restful.Request, response *restful.Response) {
	lang := helper.GetLanguageFromRequest(request.Request)

	resp := &ListPrioritiesResp{
		Items: getEnumItems(lang, priorityData),
	}

	if err := response.WriteEntity(resp); err != nil {
		zlog.SugarErrorf("Error writing response for ListTaskPriorities: %v", err)
		response.WriteErrorString(http.StatusInternalServerError, "Failed to write response for task priorities")
	}
}

func (this *TaskService) ListTaskStatuses(request *restful.Request, response *restful.Response) {
	lang := helper.GetLanguageFromRequest(request.Request)

	resp := &ListStatusesResp{
		Items: getEnumItems(lang, statusesData),
	}

	if err := response.WriteEntity(resp); err != nil {
		zlog.SugarErrorf("Error writing response for ListTaskStatuses: %v", err)
		response.WriteErrorString(http.StatusInternalServerError, "Failed to write response for task status")
	}
}

func (this *TaskService) ListQueues(request *restful.Request, response *restful.Response) {
	lang := helper.GetLanguageFromRequest(request.Request)

	resp := &ListStatusesResp{
		Items: getEnumItems(lang, queuesData),
	}

	if err := response.WriteEntity(resp); err != nil {
		zlog.SugarErrorf("Error writing response for ListQueues: %v", err)
		response.WriteErrorString(http.StatusInternalServerError, "Failed to write response for queues")
	}
}

type ListTypesResp struct {
	Items []EnumItem `json:"items"`
}

type ListPrioritiesResp struct {
	Items []EnumItem `json:"items"`
}
type ListStatusesResp struct {
	Items []EnumItem `json:"items"`
}

type ListQueuesResp struct {
	Items []EnumItem `json:"items"`
}

type EnumItem struct {
	Name        string `json:"name"`
	Value       string `json:"value"`
	Description string `json:"description"`
}

var typeData = map[string][]EnumItem{
	// 模型训练/模型量化/模型评估/模型下载/语料处理/知识加工/工作流
	helper.LANG_ZH: {
		{Value: string(model.TaskTypeModelTraining), Name: "模型训练", Description: "模型训练任务"},
		{Value: string(model.TaskTypeModelQuantization), Name: "模型量化", Description: "模型量化任务"},
		{Value: string(model.TaskTypeModelEvaluation), Name: "模型评估", Description: "模型评估任务"},
		{Value: string(model.TaskTypeModelDownload), Name: "模型下载", Description: "模型下载任务"},
		{Value: string(model.TaskTypeCorupsProcessing), Name: "语料处理", Description: "语料处理任务"},
		{Value: string(model.TaskTypePipeline), Name: "工作流", Description: "工作流任务"},
		{Value: string(model.TaskTypeKnowledgeProcessing), Name: "知识加工", Description: "知识加工任务"},
		{Value: string(model.TaskTypeUnknown), Name: "其他", Description: "其他"},
	},
	helper.LANG_EN: {
		{Value: string(model.TaskTypeModelTraining), Name: "model train", Description: "Training task"},
		{Value: string(model.TaskTypeModelQuantization), Name: "model quantization", Description: "Quantization task"},
		{Value: string(model.TaskTypeModelEvaluation), Name: "model evaluation", Description: "Model evaluation task"},
		{Value: string(model.TaskTypeModelDownload), Name: "model download", Description: "Model download task"},
		{Value: string(model.TaskTypeCorupsProcessing), Name: "corpus processing", Description: "Corpus processing task"},
		{Value: string(model.TaskTypePipeline), Name: "pipeline", Description: "Pipeline task"},
		{Value: string(model.TaskTypeKnowledgeProcessing), Name: "knowledge processing", Description: "knowledge processing task"},
		{Value: string(model.TaskTypeUnknown), Name: "other", Description: "Other task"},

		// {Value: string(model.TaskTypeInference), Name: "inference", Description: "Inference task"},
		// {Value: string(model.TaskTypeModelFineTuning), Name: "model fine-tuning", Description: "Model fine tuning task"},
		// {Value: string(model.TaskTypeCorupsEvaluation), Name: "corpus evaluation", Description: "Corpus evaluation task"},
	},
}

var priorityData = map[string][]EnumItem{
	helper.LANG_ZH: {
		{Value: string(model.TaskPriorityDefault), Name: "未知", Description: "未设置优先级"},
		{Value: string(model.TaskPriorityLow), Name: "低", Description: "低优先级任务"},
		{Value: string(model.TaskPriorityMedium), Name: "中", Description: "中优先级任务"},
		{Value: string(model.TaskPriorityHigh), Name: "高", Description: "高优先级任务"},
	},
	helper.LANG_EN: {
		{Value: string(model.TaskPriorityDefault), Name: "null", Description: "not set priority"},
		{Value: string(model.TaskPriorityLow), Name: "low", Description: "Low priority task"},
		{Value: string(model.TaskPriorityMedium), Name: "medium", Description: "Medium priority task"},
		{Value: string(model.TaskPriorityHigh), Name: "high", Description: "High priority task"},
	},
}

var statusesData = map[string][]EnumItem{
	helper.LANG_ZH: {
		{Value: string(model.TaskStatusPending), Name: "排队中", Description: "排队中"},
		// {Value: string(model.TaskStatusAdmitted), Name: "已提交", Description: "已提交"},
		{Value: string(model.TaskStatusRunning), Name: "运行中", Description: "运行中"},
		{Value: string(model.TaskStatusSucceeded), Name: "完成", Description: "完成"},
		{Value: string(model.TaskStatusFailed), Name: "失败", Description: "失败"},
		{Value: string(model.TaskStatusCancelled), Name: "已取消", Description: "已取消"},
		// {Value: string(model.TaskStatusSuspended), Name: "挂起", Description: "挂起"},
	},
	helper.LANG_EN: {
		{Value: string(model.TaskStatusPending), Name: "pending", Description: "pending"},
		// {Value: string(model.TaskStatusAdmitted), Name: "Admitted", Description: "Admitted"},
		{Value: string(model.TaskStatusRunning), Name: "running", Description: "running"},
		{Value: string(model.TaskStatusSucceeded), Name: "succeeded", Description: "succeeded"},
		{Value: string(model.TaskStatusFailed), Name: "failed", Description: "failed"},
		{Value: string(model.TaskStatusCancelled), Name: "cancelled", Description: "cancelled"},
		// {Value: string(model.TaskStatusSuspended), Name: "Suspended", Description: "Suspended"},
	},
}

var queuesData = map[string][]EnumItem{
	helper.LANG_ZH: {
		{Value: "default", Name: "默认队列", Description: "默认队列，租户服务队列"},
		{Value: "task", Name: "任务队列", Description: "任务队列"},
		{Value: "infer", Name: "服务队列", Description: "服务队列"},
	},
	helper.LANG_EN: {
		{Value: "default", Name: "default queue", Description: "default queue, tenant service queue"},
		{Value: "task", Name: "task queue", Description: "task queue"},
		{Value: "infer", Name: "inference queue", Description: "inference queue"},
	},
}

func getEnumItems(lang string, data map[string][]EnumItem) []EnumItem {
	items, ok := data[lang]
	if !ok {
		zlog.SugarWarnf("Warning: Language '%s' not fully supported, falling back to '%s'", lang, helper.LANG_ZH)
		return data[helper.LANG_ZH]
	}
	return items
}
