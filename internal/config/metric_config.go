package config

import (
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/go-viper/mapstructure/v2"
	"github.com/spf13/viper"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

const (
	MetricConfigName = "metrics"
)

var (
	MetricsCfg        *MetricsConfig
	metricsConfigOnce sync.Once
)

type metricsConfigManager struct {
	config     *MetricsConfig
	configPath string
}

func MustGetMetricsConfig() *MetricsConfig {
	metricsConfigOnce.Do(func() {
		_, err := LoadMetricsConfig("")
		if err != nil {
			panic(fmt.Sprintf("Failed to load metrics configuration: %v", err))
		}
	})

	if MetricsCfg == nil {
		panic("Failed to get metrics configuration")
	}

	return MetricsCfg
}

func LoadMetricsConfig(configPath string) (*MetricsConfig, error) {
	if configPath == "" {
		configPath = os.Getenv("MLOPS_CONF_DIR")
	}

	cm := &metricsConfigManager{
		configPath: configPath,
	}

	if err := cm.loadConfig(); err != nil {
		return nil, err
	}

	MetricsCfg = cm.config
	return cm.config, nil
}

func (cm *metricsConfigManager) loadConfig() error {
	viper.AutomaticEnv()
	viper.SetEnvPrefix(EnvPrefix)
	viper.SetConfigType(ConfigTag)

	replacer := strings.NewReplacer(".", "_")
	viper.SetEnvKeyReplacer(replacer)

	viper.AddConfigPath(cm.configPath)
	viper.AddConfigPath("./conf")
	if strings.ToUpper(os.Getenv("LOCAL_DEBUG_MODEL")) == "TRUE" {
		viper.AddConfigPath("../conf")
		viper.AddConfigPath("../../conf")
	}

	viper.SetConfigName(MetricConfigName)
	if err := viper.ReadInConfig(); err != nil {
		panic(fmt.Errorf("Load config file %s failed：%s", MetricConfigName, err.Error()))
	}

	cfg := new(MetricsConfig)
	if err := viper.Unmarshal(&cfg, func(dc *mapstructure.DecoderConfig) {
		dc.TagName = ConfigTag
	}); err != nil {
		panic(fmt.Errorf("Unmarshal config file %s failed：%s", MetricConfigName, err.Error()))
	}
	cm.setDefaults(cfg)

	if err := cm.validateConfig(cfg); err != nil {
		return fmt.Errorf("invalid config: %w", err)
	}

	zlog.SugarInfof("Loaded metrics config from %s", cm.configPath)
	cm.config = cfg
	cfg.Display()
	return nil
}

func (cm *metricsConfigManager) setDefaults(config *MetricsConfig) {
	if config.Prometheus.Timeout == 0 {
		config.Prometheus.Timeout = 30 * time.Second
	}
	if config.Prometheus.MaxRetries == 0 {
		config.Prometheus.MaxRetries = 3
	}
	if config.Prometheus.RetryDelay == 0 {
		config.Prometheus.RetryDelay = 1 * time.Second
	}

	if config.Cache.TTL == 0 {
		config.Cache.TTL = 5 * time.Minute
	}
	if config.Cache.MaxSize == 0 {
		config.Cache.MaxSize = 1000
	}
	if config.Cache.CleanupInterval == 0 {
		config.Cache.CleanupInterval = 10 * time.Minute
	}

	for name, metric := range config.Metrics {
		if metric.Interval == 0 {
			metric.Interval = 30 * time.Second
		}
		if metric.Timeout == 0 {
			metric.Timeout = 10 * time.Second
		}
		if metric.Type == "" {
			metric.Type = MetricTypeGauge
		}
		config.Metrics[name] = metric
	}
}

func (cm *metricsConfigManager) validateConfig(config *MetricsConfig) error {
	if config.Prometheus.Endpoint == "" {
		return fmt.Errorf("prometheus endpoint is required")
	}

	for name, metric := range config.Metrics {
		if metric.Query == "" {
			return fmt.Errorf("metric %s query is required", name)
		}
	}

	return nil
}

func (cm *metricsConfigManager) GetConfig() *MetricsConfig {
	return cm.config
}

func (cm *metricsConfigManager) GetMetricConfig(name string) (*MetricConfig, bool) {
	metric, exists := cm.config.Metrics[name]
	return &metric, exists
}

func (cm *metricsConfigManager) GetQueryTemplates() map[string]string {
	templates := make(map[string]string)

	for name, metric := range cm.config.Metrics {
		templates[name] = metric.Query
	}

	return templates
}

type MetricConfig struct {
	Name        string            `yaml:"name" json:"name"`
	Query       string            `yaml:"query" json:"query"`
	Description string            `yaml:"description" json:"description"`
	Unit        string            `yaml:"unit" json:"unit"`
	Type        MetricType        `yaml:"type" json:"type"`
	Labels      []string          `yaml:"labels" json:"labels"`
	Interval    time.Duration     `yaml:"interval" json:"interval"`
	Timeout     time.Duration     `yaml:"timeout" json:"timeout"`
	Tags        map[string]string `yaml:"tags" json:"tags"`
}

type MetricType string

const (
	MetricTypeGauge     MetricType = "gauge"
	MetricTypeCounter   MetricType = "counter"
	MetricTypeHistogram MetricType = "histogram"
	MetricTypeSummary   MetricType = "summary"
)

type MetricsConfig struct {
	Prometheus PrometheusConfig        `yaml:"prometheus" json:"prometheus"`
	Metrics    map[string]MetricConfig `yaml:"metrics" json:"metrics"`
	Cache      CacheConfig             `yaml:"cache" json:"cache"`
	Collectors []CollectorConfig       `yaml:"collectors" json:"collectors"`
}

func (c *MetricsConfig) Display() {
	bs, _ := json.MarshalIndent(c, "", "    ")
	zlog.SugarInfoln(string(bs))
}

type PrometheusConfig struct {
	Endpoint   string        `yaml:"endpoint" json:"endpoint"`
	Username   string        `yaml:"username" json:"username"`
	Password   string        `yaml:"password" json:"password"`
	Timeout    time.Duration `yaml:"timeout" json:"timeout"`
	MaxRetries int           `yaml:"max_retries" json:"max_retries"`
	RetryDelay time.Duration `yaml:"retry_delay" json:"retry_delay"`
	EnableTLS  bool          `yaml:"enable_tls" json:"enable_tls"`
	TLSConfig  TLSConfig     `yaml:"tls_config" json:"tls_config"`
}

type TLSConfig struct {
	CertFile           string `yaml:"cert_file" json:"cert_file"`
	KeyFile            string `yaml:"key_file" json:"key_file"`
	CAFile             string `yaml:"ca_file" json:"ca_file"`
	InsecureSkipVerify bool   `yaml:"insecure_skip_verify" json:"insecure_skip_verify"`
}

type CacheConfig struct {
	Enabled         bool          `yaml:"enabled" json:"enabled"`
	TTL             time.Duration `yaml:"ttl" json:"ttl"`
	MaxSize         int           `yaml:"max_size" json:"max_size"`
	Interval        time.Duration `yaml:"interval" json:"interval"`
	CleanupInterval time.Duration `yaml:"cleanup_interval" json:"cleanup_interval"`
}

type CollectorConfig struct {
	Name        string        `yaml:"name" json:"name"`
	Enabled     bool          `yaml:"enabled" json:"enabled"`
	Interval    time.Duration `yaml:"interval" json:"interval"`
	MetricNames []string      `yaml:"metric_names" json:"metric_names"`
}
