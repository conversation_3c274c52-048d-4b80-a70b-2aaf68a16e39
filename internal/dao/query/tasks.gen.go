// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"transwarp.io/mlops/pipeline/internal/dao/model"
)

func newTask(db *gorm.DB, opts ...gen.DOOption) task {
	_task := task{}

	_task.taskDo.UseDB(db, opts...)
	_task.taskDo.UseModel(&model.Task{})

	tableName := _task.taskDo.TableName()
	_task.ALL = field.NewAsterisk(tableName)
	_task.ID = field.NewString(tableName, "id")
	_task.Name = field.NewString(tableName, "name")
	_task.Type = field.NewString(tableName, "type")
	_task.Priority = field.NewString(tableName, "priority")
	_task.Description = field.NewString(tableName, "description")
	_task.Creator = field.NewString(tableName, "creator")
	_task.CreatedAt = field.NewTime(tableName, "created_at")
	_task.UpdatedAt = field.NewTime(tableName, "updated_at")
	_task.Status = field.NewString(tableName, "status")
	_task.StartedAt = field.NewTime(tableName, "started_at")
	_task.EndedAt = field.NewTime(tableName, "ended_at")
	_task.TenantID = field.NewString(tableName, "tenant_id")
	_task.TenantName = field.NewString(tableName, "tenant_name")
	_task.ProjectID = field.NewString(tableName, "project_id")
	_task.ProjectName = field.NewString(tableName, "project_name")
	_task.YamlRawManifest = field.NewString(tableName, "yaml_raw_manifest")
	_task.RefSourceID = field.NewString(tableName, "ref_source_id")
	_task.Extra = field.NewString(tableName, "extra")
	_task.QueueName = field.NewString(tableName, "queue_name")
	_task.ClusterQueueName = field.NewString(tableName, "cluster_queue_name")
	_task.Namespace = field.NewString(tableName, "namespace")
	_task.Labels = field.NewString(tableName, "labels")
	_task.Annotations = field.NewString(tableName, "annotations")
	_task.Gvk = field.NewString(tableName, "gvk")
	_task.Gvr = field.NewString(tableName, "gvr")
	_task.RefRunID = field.NewString(tableName, "ref_run_id")
	_task.Resources = field.NewString(tableName, "resources")

	_task.fillFieldMap()

	return _task
}

type task struct {
	taskDo

	ALL              field.Asterisk
	ID               field.String // ID
	Name             field.String // Name
	Type             field.String // Type
	Priority         field.String // Priority
	Description      field.String // Description
	Creator          field.String // Creator
	CreatedAt        field.Time   // create time
	UpdatedAt        field.Time   // update time
	Status           field.String // Status
	StartedAt        field.Time   // start time
	EndedAt          field.Time   // end time
	TenantID         field.String // TenantID
	TenantName       field.String // TenantName
	ProjectID        field.String // ProjectID
	ProjectName      field.String // ProjectName
	YamlRawManifest  field.String // RawManifest
	RefSourceID      field.String // RefSourceID
	Extra            field.String // Extra json
	QueueName        field.String // QueueName
	ClusterQueueName field.String // ClusterQueueName
	Namespace        field.String // Namespace
	Labels           field.String // Labels json
	Annotations      field.String // Annotations json
	Gvk              field.String // GVK json
	Gvr              field.String // GVR json
	RefRunID         field.String // RefRunID
	Resources        field.String // Resources json

	fieldMap map[string]field.Expr
}

func (t task) Table(newTableName string) *task {
	t.taskDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t task) As(alias string) *task {
	t.taskDo.DO = *(t.taskDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *task) updateTableName(table string) *task {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewString(table, "id")
	t.Name = field.NewString(table, "name")
	t.Type = field.NewString(table, "type")
	t.Priority = field.NewString(table, "priority")
	t.Description = field.NewString(table, "description")
	t.Creator = field.NewString(table, "creator")
	t.CreatedAt = field.NewTime(table, "created_at")
	t.UpdatedAt = field.NewTime(table, "updated_at")
	t.Status = field.NewString(table, "status")
	t.StartedAt = field.NewTime(table, "started_at")
	t.EndedAt = field.NewTime(table, "ended_at")
	t.TenantID = field.NewString(table, "tenant_id")
	t.TenantName = field.NewString(table, "tenant_name")
	t.ProjectID = field.NewString(table, "project_id")
	t.ProjectName = field.NewString(table, "project_name")
	t.YamlRawManifest = field.NewString(table, "yaml_raw_manifest")
	t.RefSourceID = field.NewString(table, "ref_source_id")
	t.Extra = field.NewString(table, "extra")
	t.QueueName = field.NewString(table, "queue_name")
	t.ClusterQueueName = field.NewString(table, "cluster_queue_name")
	t.Namespace = field.NewString(table, "namespace")
	t.Labels = field.NewString(table, "labels")
	t.Annotations = field.NewString(table, "annotations")
	t.Gvk = field.NewString(table, "gvk")
	t.Gvr = field.NewString(table, "gvr")
	t.RefRunID = field.NewString(table, "ref_run_id")
	t.Resources = field.NewString(table, "resources")

	t.fillFieldMap()

	return t
}

func (t *task) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *task) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 27)
	t.fieldMap["id"] = t.ID
	t.fieldMap["name"] = t.Name
	t.fieldMap["type"] = t.Type
	t.fieldMap["priority"] = t.Priority
	t.fieldMap["description"] = t.Description
	t.fieldMap["creator"] = t.Creator
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["updated_at"] = t.UpdatedAt
	t.fieldMap["status"] = t.Status
	t.fieldMap["started_at"] = t.StartedAt
	t.fieldMap["ended_at"] = t.EndedAt
	t.fieldMap["tenant_id"] = t.TenantID
	t.fieldMap["tenant_name"] = t.TenantName
	t.fieldMap["project_id"] = t.ProjectID
	t.fieldMap["project_name"] = t.ProjectName
	t.fieldMap["yaml_raw_manifest"] = t.YamlRawManifest
	t.fieldMap["ref_source_id"] = t.RefSourceID
	t.fieldMap["extra"] = t.Extra
	t.fieldMap["queue_name"] = t.QueueName
	t.fieldMap["cluster_queue_name"] = t.ClusterQueueName
	t.fieldMap["namespace"] = t.Namespace
	t.fieldMap["labels"] = t.Labels
	t.fieldMap["annotations"] = t.Annotations
	t.fieldMap["gvk"] = t.Gvk
	t.fieldMap["gvr"] = t.Gvr
	t.fieldMap["ref_run_id"] = t.RefRunID
	t.fieldMap["resources"] = t.Resources
}

func (t task) clone(db *gorm.DB) task {
	t.taskDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t task) replaceDB(db *gorm.DB) task {
	t.taskDo.ReplaceDB(db)
	return t
}

type taskDo struct{ gen.DO }

func (t taskDo) Debug() *taskDo {
	return t.withDO(t.DO.Debug())
}

func (t taskDo) WithContext(ctx context.Context) *taskDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t taskDo) ReadDB() *taskDo {
	return t.Clauses(dbresolver.Read)
}

func (t taskDo) WriteDB() *taskDo {
	return t.Clauses(dbresolver.Write)
}

func (t taskDo) Session(config *gorm.Session) *taskDo {
	return t.withDO(t.DO.Session(config))
}

func (t taskDo) Clauses(conds ...clause.Expression) *taskDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t taskDo) Returning(value interface{}, columns ...string) *taskDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t taskDo) Not(conds ...gen.Condition) *taskDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t taskDo) Or(conds ...gen.Condition) *taskDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t taskDo) Select(conds ...field.Expr) *taskDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t taskDo) Where(conds ...gen.Condition) *taskDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t taskDo) Order(conds ...field.Expr) *taskDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t taskDo) Distinct(cols ...field.Expr) *taskDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t taskDo) Omit(cols ...field.Expr) *taskDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t taskDo) Join(table schema.Tabler, on ...field.Expr) *taskDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t taskDo) LeftJoin(table schema.Tabler, on ...field.Expr) *taskDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t taskDo) RightJoin(table schema.Tabler, on ...field.Expr) *taskDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t taskDo) Group(cols ...field.Expr) *taskDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t taskDo) Having(conds ...gen.Condition) *taskDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t taskDo) Limit(limit int) *taskDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t taskDo) Offset(offset int) *taskDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t taskDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *taskDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t taskDo) Unscoped() *taskDo {
	return t.withDO(t.DO.Unscoped())
}

func (t taskDo) Create(values ...*model.Task) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t taskDo) CreateInBatches(values []*model.Task, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t taskDo) Save(values ...*model.Task) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t taskDo) First() (*model.Task, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Task), nil
	}
}

func (t taskDo) Take() (*model.Task, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Task), nil
	}
}

func (t taskDo) Last() (*model.Task, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Task), nil
	}
}

func (t taskDo) Find() ([]*model.Task, error) {
	result, err := t.DO.Find()
	return result.([]*model.Task), err
}

func (t taskDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Task, err error) {
	buf := make([]*model.Task, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t taskDo) FindInBatches(result *[]*model.Task, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t taskDo) Attrs(attrs ...field.AssignExpr) *taskDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t taskDo) Assign(attrs ...field.AssignExpr) *taskDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t taskDo) Joins(fields ...field.RelationField) *taskDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t taskDo) Preload(fields ...field.RelationField) *taskDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t taskDo) FirstOrInit() (*model.Task, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Task), nil
	}
}

func (t taskDo) FirstOrCreate() (*model.Task, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Task), nil
	}
}

func (t taskDo) FindByPage(offset int, limit int) (result []*model.Task, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t taskDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t taskDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t taskDo) Delete(models ...*model.Task) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *taskDo) withDO(do gen.Dao) *taskDo {
	t.DO = *do.(*gen.DO)
	return t
}
