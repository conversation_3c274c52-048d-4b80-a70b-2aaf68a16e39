// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:              db,
		Metric:          newMetric(db, opts...),
		Pipeline:        newPipeline(db, opts...),
		PipelineRun:     newPipelineRun(db, opts...),
		PipelineVersion: newPipelineVersion(db, opts...),
		Run:             newRun(db, opts...),
		Task:            newTask(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	Metric          metric
	Pipeline        pipeline
	PipelineRun     pipelineRun
	PipelineVersion pipelineVersion
	Run             run
	Task            task
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:              db,
		Metric:          q.Metric.clone(db),
		Pipeline:        q.Pipeline.clone(db),
		PipelineRun:     q.PipelineRun.clone(db),
		PipelineVersion: q.PipelineVersion.clone(db),
		Run:             q.Run.clone(db),
		Task:            q.Task.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:              db,
		Metric:          q.Metric.replaceDB(db),
		Pipeline:        q.Pipeline.replaceDB(db),
		PipelineRun:     q.PipelineRun.replaceDB(db),
		PipelineVersion: q.PipelineVersion.replaceDB(db),
		Run:             q.Run.replaceDB(db),
		Task:            q.Task.replaceDB(db),
	}
}

type queryCtx struct {
	Metric          *metricDo
	Pipeline        *pipelineDo
	PipelineRun     *pipelineRunDo
	PipelineVersion *pipelineVersionDo
	Run             *runDo
	Task            *taskDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		Metric:          q.Metric.WithContext(ctx),
		Pipeline:        q.Pipeline.WithContext(ctx),
		PipelineRun:     q.PipelineRun.WithContext(ctx),
		PipelineVersion: q.PipelineVersion.WithContext(ctx),
		Run:             q.Run.WithContext(ctx),
		Task:            q.Task.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
