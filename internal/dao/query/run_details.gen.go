// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"transwarp.io/mlops/pipeline/internal/dao/model"
)

func newPipelineRun(db *gorm.DB, opts ...gen.DOOption) pipelineRun {
	_pipelineRun := pipelineRun{}

	_pipelineRun.pipelineRunDo.UseDB(db, opts...)
	_pipelineRun.pipelineRunDo.UseModel(&model.PipelineRun{})

	tableName := _pipelineRun.pipelineRunDo.TableName()
	_pipelineRun.ALL = field.NewAsterisk(tableName)
	_pipelineRun.UUID = field.NewString(tableName, "UUID")
	_pipelineRun.ExperimentUUID = field.NewString(tableName, "ExperimentUUID")
	_pipelineRun.DisplayName = field.NewString(tableName, "DisplayName")
	_pipelineRun.Name = field.NewString(tableName, "Name")
	_pipelineRun.StorageState = field.NewString(tableName, "StorageState")
	_pipelineRun.Namespace = field.NewString(tableName, "Namespace")
	_pipelineRun.ServiceAccount = field.NewString(tableName, "ServiceAccount")
	_pipelineRun.Description = field.NewString(tableName, "Description")
	_pipelineRun.CreatedAtInSec = field.NewInt64(tableName, "CreatedAtInSec")
	_pipelineRun.ScheduledAtInSec = field.NewInt64(tableName, "ScheduledAtInSec")
	_pipelineRun.FinishedAtInSec = field.NewInt64(tableName, "FinishedAtInSec")
	_pipelineRun.Conditions = field.NewString(tableName, "Conditions")
	_pipelineRun.PipelineID = field.NewString(tableName, "PipelineId")
	_pipelineRun.PipelineName = field.NewString(tableName, "PipelineName")
	_pipelineRun.PipelineSpecManifest = field.NewString(tableName, "PipelineSpecManifest")
	_pipelineRun.WorkflowSpecManifest = field.NewString(tableName, "WorkflowSpecManifest")
	_pipelineRun.Parameters = field.NewString(tableName, "Parameters")
	_pipelineRun.PipelineRuntimeManifest = field.NewString(tableName, "PipelineRuntimeManifest")
	_pipelineRun.WorkflowRuntimeManifest = field.NewString(tableName, "WorkflowRuntimeManifest")
	_pipelineRun.SophonID = field.NewString(tableName, "SophonId")
	_pipelineRun.ProjectID = field.NewString(tableName, "ProjectId")
	_pipelineRun.SourceType = field.NewString(tableName, "SourceType")
	_pipelineRun.Owner = field.NewString(tableName, "Owner")
	_pipelineRun.SourceID = field.NewString(tableName, "SourceId")
	_pipelineRun.SourceName = field.NewString(tableName, "SourceName")

	_pipelineRun.fillFieldMap()

	return _pipelineRun
}

type pipelineRun struct {
	pipelineRunDo

	ALL                     field.Asterisk
	UUID                    field.String
	ExperimentUUID          field.String
	DisplayName             field.String
	Name                    field.String
	StorageState            field.String
	Namespace               field.String
	ServiceAccount          field.String
	Description             field.String
	CreatedAtInSec          field.Int64
	ScheduledAtInSec        field.Int64
	FinishedAtInSec         field.Int64
	Conditions              field.String
	PipelineID              field.String
	PipelineName            field.String
	PipelineSpecManifest    field.String
	WorkflowSpecManifest    field.String
	Parameters              field.String
	PipelineRuntimeManifest field.String
	WorkflowRuntimeManifest field.String
	SophonID                field.String
	ProjectID               field.String
	SourceType              field.String
	Owner                   field.String
	SourceID                field.String
	SourceName              field.String

	fieldMap map[string]field.Expr
}

func (p pipelineRun) Table(newTableName string) *pipelineRun {
	p.pipelineRunDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p pipelineRun) As(alias string) *pipelineRun {
	p.pipelineRunDo.DO = *(p.pipelineRunDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *pipelineRun) updateTableName(table string) *pipelineRun {
	p.ALL = field.NewAsterisk(table)
	p.UUID = field.NewString(table, "UUID")
	p.ExperimentUUID = field.NewString(table, "ExperimentUUID")
	p.DisplayName = field.NewString(table, "DisplayName")
	p.Name = field.NewString(table, "Name")
	p.StorageState = field.NewString(table, "StorageState")
	p.Namespace = field.NewString(table, "Namespace")
	p.ServiceAccount = field.NewString(table, "ServiceAccount")
	p.Description = field.NewString(table, "Description")
	p.CreatedAtInSec = field.NewInt64(table, "CreatedAtInSec")
	p.ScheduledAtInSec = field.NewInt64(table, "ScheduledAtInSec")
	p.FinishedAtInSec = field.NewInt64(table, "FinishedAtInSec")
	p.Conditions = field.NewString(table, "Conditions")
	p.PipelineID = field.NewString(table, "PipelineId")
	p.PipelineName = field.NewString(table, "PipelineName")
	p.PipelineSpecManifest = field.NewString(table, "PipelineSpecManifest")
	p.WorkflowSpecManifest = field.NewString(table, "WorkflowSpecManifest")
	p.Parameters = field.NewString(table, "Parameters")
	p.PipelineRuntimeManifest = field.NewString(table, "PipelineRuntimeManifest")
	p.WorkflowRuntimeManifest = field.NewString(table, "WorkflowRuntimeManifest")
	p.SophonID = field.NewString(table, "SophonId")
	p.ProjectID = field.NewString(table, "ProjectId")
	p.SourceType = field.NewString(table, "SourceType")
	p.Owner = field.NewString(table, "Owner")
	p.SourceID = field.NewString(table, "SourceId")
	p.SourceName = field.NewString(table, "SourceName")

	p.fillFieldMap()

	return p
}

func (p *pipelineRun) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *pipelineRun) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 25)
	p.fieldMap["UUID"] = p.UUID
	p.fieldMap["ExperimentUUID"] = p.ExperimentUUID
	p.fieldMap["DisplayName"] = p.DisplayName
	p.fieldMap["Name"] = p.Name
	p.fieldMap["StorageState"] = p.StorageState
	p.fieldMap["Namespace"] = p.Namespace
	p.fieldMap["ServiceAccount"] = p.ServiceAccount
	p.fieldMap["Description"] = p.Description
	p.fieldMap["CreatedAtInSec"] = p.CreatedAtInSec
	p.fieldMap["ScheduledAtInSec"] = p.ScheduledAtInSec
	p.fieldMap["FinishedAtInSec"] = p.FinishedAtInSec
	p.fieldMap["Conditions"] = p.Conditions
	p.fieldMap["PipelineId"] = p.PipelineID
	p.fieldMap["PipelineName"] = p.PipelineName
	p.fieldMap["PipelineSpecManifest"] = p.PipelineSpecManifest
	p.fieldMap["WorkflowSpecManifest"] = p.WorkflowSpecManifest
	p.fieldMap["Parameters"] = p.Parameters
	p.fieldMap["PipelineRuntimeManifest"] = p.PipelineRuntimeManifest
	p.fieldMap["WorkflowRuntimeManifest"] = p.WorkflowRuntimeManifest
	p.fieldMap["SophonId"] = p.SophonID
	p.fieldMap["ProjectId"] = p.ProjectID
	p.fieldMap["SourceType"] = p.SourceType
	p.fieldMap["Owner"] = p.Owner
	p.fieldMap["SourceId"] = p.SourceID
	p.fieldMap["SourceName"] = p.SourceName
}

func (p pipelineRun) clone(db *gorm.DB) pipelineRun {
	p.pipelineRunDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p pipelineRun) replaceDB(db *gorm.DB) pipelineRun {
	p.pipelineRunDo.ReplaceDB(db)
	return p
}

type pipelineRunDo struct{ gen.DO }

func (p pipelineRunDo) Debug() *pipelineRunDo {
	return p.withDO(p.DO.Debug())
}

func (p pipelineRunDo) WithContext(ctx context.Context) *pipelineRunDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p pipelineRunDo) ReadDB() *pipelineRunDo {
	return p.Clauses(dbresolver.Read)
}

func (p pipelineRunDo) WriteDB() *pipelineRunDo {
	return p.Clauses(dbresolver.Write)
}

func (p pipelineRunDo) Session(config *gorm.Session) *pipelineRunDo {
	return p.withDO(p.DO.Session(config))
}

func (p pipelineRunDo) Clauses(conds ...clause.Expression) *pipelineRunDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p pipelineRunDo) Returning(value interface{}, columns ...string) *pipelineRunDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p pipelineRunDo) Not(conds ...gen.Condition) *pipelineRunDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p pipelineRunDo) Or(conds ...gen.Condition) *pipelineRunDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p pipelineRunDo) Select(conds ...field.Expr) *pipelineRunDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p pipelineRunDo) Where(conds ...gen.Condition) *pipelineRunDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p pipelineRunDo) Order(conds ...field.Expr) *pipelineRunDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p pipelineRunDo) Distinct(cols ...field.Expr) *pipelineRunDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p pipelineRunDo) Omit(cols ...field.Expr) *pipelineRunDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p pipelineRunDo) Join(table schema.Tabler, on ...field.Expr) *pipelineRunDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p pipelineRunDo) LeftJoin(table schema.Tabler, on ...field.Expr) *pipelineRunDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p pipelineRunDo) RightJoin(table schema.Tabler, on ...field.Expr) *pipelineRunDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p pipelineRunDo) Group(cols ...field.Expr) *pipelineRunDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p pipelineRunDo) Having(conds ...gen.Condition) *pipelineRunDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p pipelineRunDo) Limit(limit int) *pipelineRunDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p pipelineRunDo) Offset(offset int) *pipelineRunDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p pipelineRunDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *pipelineRunDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p pipelineRunDo) Unscoped() *pipelineRunDo {
	return p.withDO(p.DO.Unscoped())
}

func (p pipelineRunDo) Create(values ...*model.PipelineRun) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p pipelineRunDo) CreateInBatches(values []*model.PipelineRun, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p pipelineRunDo) Save(values ...*model.PipelineRun) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p pipelineRunDo) First() (*model.PipelineRun, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.PipelineRun), nil
	}
}

func (p pipelineRunDo) Take() (*model.PipelineRun, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.PipelineRun), nil
	}
}

func (p pipelineRunDo) Last() (*model.PipelineRun, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.PipelineRun), nil
	}
}

func (p pipelineRunDo) Find() ([]*model.PipelineRun, error) {
	result, err := p.DO.Find()
	return result.([]*model.PipelineRun), err
}

func (p pipelineRunDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PipelineRun, err error) {
	buf := make([]*model.PipelineRun, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p pipelineRunDo) FindInBatches(result *[]*model.PipelineRun, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p pipelineRunDo) Attrs(attrs ...field.AssignExpr) *pipelineRunDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p pipelineRunDo) Assign(attrs ...field.AssignExpr) *pipelineRunDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p pipelineRunDo) Joins(fields ...field.RelationField) *pipelineRunDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p pipelineRunDo) Preload(fields ...field.RelationField) *pipelineRunDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p pipelineRunDo) FirstOrInit() (*model.PipelineRun, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.PipelineRun), nil
	}
}

func (p pipelineRunDo) FirstOrCreate() (*model.PipelineRun, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.PipelineRun), nil
	}
}

func (p pipelineRunDo) FindByPage(offset int, limit int) (result []*model.PipelineRun, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p pipelineRunDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p pipelineRunDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p pipelineRunDo) Delete(models ...*model.PipelineRun) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *pipelineRunDo) withDO(do gen.Dao) *pipelineRunDo {
	p.DO = *do.(*gen.DO)
	return p
}
