// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameTask = "tasks"

// Task mapped from table <tasks>
type Task struct {
	ID               string     `gorm:"column:id;primaryKey;comment:ID" json:"id"`                                         // ID
	Name             string     `gorm:"column:name;comment:Name" json:"name"`                                              // Name
	Type             string     `gorm:"column:type;comment:Type" json:"type"`                                              // Type
	Priority         string     `gorm:"column:priority;comment:Priority" json:"priority"`                                  // Priority
	Description      string     `gorm:"column:description;comment:Description" json:"description"`                         // Description
	Creator          string     `gorm:"column:creator;comment:Creator" json:"creator"`                                     // Creator
	CreatedAt        time.Time  `gorm:"column:created_at;default:CURRENT_TIMESTAMP;comment:create time" json:"created_at"` // create time
	UpdatedAt        time.Time  `gorm:"column:updated_at;default:CURRENT_TIMESTAMP;comment:update time" json:"updated_at"` // update time
	Status           string     `gorm:"column:status;comment:Status" json:"status"`                                        // Status
	StartedAt        *time.Time `gorm:"column:started_at;comment:start time" json:"started_at"`                            // start time
	EndedAt          *time.Time `gorm:"column:ended_at;comment:end time" json:"ended_at"`                                  // end time
	TenantID         string     `gorm:"column:tenant_id;comment:TenantID" json:"tenant_id"`                                // TenantID
	TenantName       string     `gorm:"column:tenant_name;comment:TenantName" json:"tenant_name"`                          // TenantName
	ProjectID        string     `gorm:"column:project_id;comment:ProjectID" json:"project_id"`                             // ProjectID
	ProjectName      string     `gorm:"column:project_name;comment:ProjectName" json:"project_name"`                       // ProjectName
	YamlRawManifest  string     `gorm:"column:yaml_raw_manifest;comment:RawManifest" json:"yaml_raw_manifest"`             // RawManifest
	RefSourceID      string     `gorm:"column:ref_source_id;comment:RefSourceID" json:"ref_source_id"`                     // RefSourceID
	Extra            string     `gorm:"column:extra;comment:Extra json" json:"extra"`                                      // Extra json
	QueueName        string     `gorm:"column:queue_name;comment:QueueName" json:"queue_name"`                             // QueueName
	ClusterQueueName string     `gorm:"column:cluster_queue_name;comment:ClusterQueueName" json:"cluster_queue_name"`      // ClusterQueueName
	Namespace        string     `gorm:"column:namespace;comment:Namespace" json:"namespace"`                               // Namespace
	Labels           string     `gorm:"column:labels;comment:Labels json" json:"labels"`                                   // Labels json
	Annotations      string     `gorm:"column:annotations;comment:Annotations json" json:"annotations"`                    // Annotations json
	Gvk              string     `gorm:"column:gvk;comment:GVK json" json:"gvk"`                                            // GVK json
	Gvr              string     `gorm:"column:gvr;comment:GVR json" json:"gvr"`                                            // GVR json
	RefRunID         string     `gorm:"column:ref_run_id;comment:RefRunID" json:"ref_run_id"`                              // RefRunID
	Resources        string     `gorm:"column:resources;comment:Resources json" json:"resources"`                          // Resources json
}

// TableName Task's table name
func (*Task) TableName() string {
	return TableNameTask
}
