// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameRun = "runs"

// Run mapped from table <runs>
type Run struct {
	UUID                    string `gorm:"column:UUID;primaryKey" json:"UUID"`
	Name                    string `gorm:"column:Name;not null" json:"Name"`
	Namespace               string `gorm:"column:Namespace;not null" json:"Namespace"`
	CreatedAtInSec          int64  `gorm:"column:CreatedAtInSec;not null" json:"CreatedAtInSec"`
	ScheduledAtInSec        int64  `gorm:"column:ScheduledAtInSec" json:"ScheduledAtInSec"`
	FinishedAtInSec         int64  `gorm:"column:FinishedAtInSec" json:"FinishedAtInSec"`
	Conditions              string `gorm:"column:Conditions;not null" json:"Conditions"`
	WorkflowSpecManifest    string `gorm:"column:WorkflowSpecManifest;not null" json:"WorkflowSpecManifest"`
	WorkflowRuntimeManifest string `gorm:"column:WorkflowRuntimeManifest;not null" json:"WorkflowRuntimeManifest"`
	ProjectID               string `gorm:"column:ProjectId" json:"ProjectId"`
	SourceType              string `gorm:"column:SourceType" json:"SourceType"`
	Owner                   string `gorm:"column:Owner" json:"Owner"`
	SourceID                string `gorm:"column:SourceId" json:"SourceId"`
	SourceName              string `gorm:"column:SourceName" json:"SourceName"`
}

// TableName Run's table name
func (*Run) TableName() string {
	return TableNameRun
}
