package dao

import (
	"context"
	"fmt"
	"os"
	"strings"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	pb "transwarp.io/aip/llmops-common/pb/pipeline"
	"transwarp.io/mlops/mlops-std/conf"
	"transwarp.io/mlops/mlops-std/database"
	"transwarp.io/mlops/mlops-std/stderr"
	stdutil "transwarp.io/mlops/mlops-std/util"
	config "transwarp.io/mlops/pipeline/internal/config"
	"transwarp.io/mlops/pipeline/internal/dao/model"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
	"transwarp.io/mlops/pipeline/internal/util"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

var PipelineDAOInst *PipelineDAO
var PipelineVersionDAOInst *PipelineVersionDAO
var RunDAOInst *RunDAO
var MetricDAOInst *MetricDAO
var TaskDAOInst *TaskDAO

var (
	mysqlDAO *CommonDAO
)

func MustGetPipelineDAO() *PipelineDAO {
	if PipelineDAOInst == nil {
		Init()
	}
	return PipelineDAOInst
}

func MustGetPipelineVersionDAO() *PipelineVersionDAO {
	if PipelineVersionDAOInst == nil {
		Init()
	}
	return PipelineVersionDAOInst
}

func MustGetMetricDAO() *MetricDAO {
	if MetricDAOInst == nil {
		Init()
	}
	return MetricDAOInst
}

func MustGetRunDAO() *RunDAO {
	if RunDAOInst == nil {
		Init()
	}
	return RunDAOInst
}

func MustGetTaskDAO() *TaskDAO {
	if TaskDAOInst == nil {
		Init()
	}
	return TaskDAOInst
}

func Init() {
	metaDb := database.CreateDB(config.MustGetConfig().MLOPS.Datasource)

	dao, err := NewDAO(metaDb)
	if err != nil {
		zlog.SugarErrorf("failed to new mysql dao, err:%+v", err)
	}
	mysqlDAO = dao
	mysqlDAO.AutoMigrate()

	PipelineDAOInst = NewPipelineDAO(mysqlDAO)
	PipelineVersionDAOInst = NewPipelineVersionDAO(mysqlDAO)
	MetricDAOInst = NewMetricDAO(mysqlDAO)
	RunDAOInst = NewRunDAO(mysqlDAO)
	TaskDAOInst = NewTaskDAO(mysqlDAO)
	upgrade()
}

func upgrade() {
	mlpipelineDatasource, err := initMlpipelineDatasource()
	if err != nil {
		zlog.SugarWarnf("[upgrade]skipping migrate runs, err: %+v", err)
		return
	}
	_, count, err := RunDAOInst.List("", &pb.ListRunsReq_Filter{}, nil)
	if err != nil {
		zlog.SugarWarnf("[upgrade]skipping migrate runs, err: %+v", err)
		return
	}
	if count > 0 {
		zlog.SugarWarnf("run data already exists, skip migration")
		return
	}
	zlog.SugarInfof("init ml pipeline db config: %s", stdutil.ToJson(mlpipelineDatasource))
	mlPipelineSb := database.CreateDB(*mlpipelineDatasource)
	pipelineDAO, err := NewDAO(mlPipelineSb)
	if err != nil {
		zlog.SugarWarnf("skipping migrate runs, err: %+v", err)
	}
	pipelineRunDAOInst := NewRunDAO(pipelineDAO)
	pipelineRuns, err := pipelineRunDAOInst.PipelineRun.Find()
	if err != nil {
		zlog.SugarWarnf("skipping migrate runs, err: %+v", err)
	}
	for _, pRun := range pipelineRuns {
		if pRun.SophonID != util.GetCurrentNamespace() {
			continue
		}
		run := &model.Run{
			UUID:                    pRun.UUID,
			Name:                    pRun.Name,
			Namespace:               pRun.Namespace,
			CreatedAtInSec:          pRun.CreatedAtInSec,
			ScheduledAtInSec:        pRun.ScheduledAtInSec,
			FinishedAtInSec:         pRun.FinishedAtInSec,
			Conditions:              pRun.Conditions,
			WorkflowSpecManifest:    pRun.WorkflowSpecManifest,
			WorkflowRuntimeManifest: pRun.WorkflowRuntimeManifest,
			ProjectID:               pRun.ProjectID,
			SourceType:              pRun.SophonID,
			Owner:                   pRun.Owner,
			SourceID:                pRun.SophonID,
			SourceName:              pRun.SourceName,
		}
		err := RunDAOInst.Save(run)
		if err != nil {
			zlog.SugarErrorf("[upgrade]failed to save run, %s=%s", "run", run.UUID)
		}
	}

}

func initMlpipelineDatasource() (*conf.Datasource, error) {
	var dbHost string
	var dbPort string
	var username []byte
	var password []byte
	configMaps, err := client.MustGetK8sClient().Clientset.CoreV1().ConfigMaps(config.MustGetConfig().MLOPS.Pipeline.Namespace).List(context.Background(), metav1.ListOptions{
		LabelSelector: fmt.Sprintf("%s=%s", LabelKeyApplicationCrdId, MlPipelineLabelValueApplicationCrdId),
	})
	if err != nil {
		return nil, err
	}
	if configMaps.Items == nil || len(configMaps.Items) == 0 {
		return nil, stderr.PipelineConfigMapIsNotFound.Error()
	}

	for _, cm := range configMaps.Items {
		if strings.HasPrefix(cm.Name, "pipeline-install-config") {
			dbHost = cm.Data["dbHost"]
			dbPort = cm.Data["dbPort"]
		}
	}

	secrets, err := client.MustGetK8sClient().Clientset.CoreV1().Secrets(config.MustGetConfig().MLOPS.Pipeline.Namespace).List(context.Background(), metav1.ListOptions{
		LabelSelector: fmt.Sprintf("%s=%s", LabelKeyApplicationCrdId, MlPipelineLabelValueApplicationCrdId),
	})
	if err != nil {
		return nil, err
	}
	if secrets.Items == nil || len(secrets.Items) == 0 {
		return nil, stderr.PipelineSecretIsNotFound.Error()
	}
	for _, secret := range secrets.Items {
		if strings.HasPrefix(secret.Name, "mysql-secret") {
			username = secret.Data["username"]
			password = secret.Data["password"]
		}
	}

	if dbHost == "" || dbPort == "" || string(username) == "" || string(password) == "" {
		zlog.SugarErrorf("dbHost", dbHost, "dbPort", dbPort, "username", username, "password", password)
		return nil, stderr.PipelineMlDbConfigMiss.Error()
	}
	if os.Getenv("LOCAL_DEBUG_MODEL") == "TRUE" {
		dbHost = config.MustGetConfig().MLOPS.Datasource.Host
		dbPort = config.MustGetConfig().MLOPS.Datasource.Port
	}

	return &conf.Datasource{
		Driver:         database.MySQL,
		Username:       string(username),
		Password:       string(password),
		Host:           dbHost,
		Port:           dbPort,
		DBName:         MlPipelineDbName,
		MaxIdle:        20,
		MaxConn:        10,
		NotPrintSql:    false,
		NotCreateTable: false,
	}, nil

}
