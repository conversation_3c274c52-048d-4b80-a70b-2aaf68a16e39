package main

import (
	"gorm.io/gen"
	"gorm.io/gorm"
	mysql "transwarp.io/mlops/mlops-std/database/kundb"
)

var db *gorm.DB
var runDb *gorm.DB

func init() {
	// db, _ = gorm.Open(mysql.Open("root:Warp!CV@2022#@tcp(*************:31276)/metastore_pipeline_llmops?charset=utf8&parseTime=True"))
	// runDb, _ = gorm.Open(mysql.Open("root:Warp!CV@2022#@tcp(*************:31276)/mlpipeline?charset=utf8&parseTime=True"))
	// dev
	db, _ = gorm.Open(mysql.Open("root:Warp!CV@2022#@tcp(*************:32441)/metastore_pipeline_llmops?charset=utf8&parseTime=True"))
	runDb, _ = gorm.Open(mysql.Open("root:Warp!CV@2022#@tcp(*************:32441)/mlpipeline?charset=utf8&parseTime=True"))
}

func main() {
	g := gen.NewGenerator(gen.Config{
		OutPath:      "../../query",
		ModelPkgPath: "../../model",
		Mode:         gen.WithoutContext,
	})

	g.UseDB(db)

	// generate all table from database
	g.ApplyBasic(
		g.GenerateModel("pipelines"),
		g.GenerateModel("pipeline_versions"),
		g.GenerateModel("metrics"),
		g.GenerateModel("tasks",
			gen.FieldType("started_at", "*time.Time"),
			gen.FieldType("ended_at", "*time.Time"),
			gen.FieldIgnore("node_names"),
			gen.FieldIgnore("sub_tasks"),
			gen.FieldIgnore("rank"),
			gen.FieldIgnore("pod_count"),
			gen.FieldIgnore("queued_duration"),
		),
		g.GenerateModel("runs"),
	)
	g.Execute()

	g.UseDB(runDb)

	// generate all table from database
	g.ApplyBasic(
		g.GenerateModelAs("run_details", "PipelineRun"),
	)
	g.Execute()
}
