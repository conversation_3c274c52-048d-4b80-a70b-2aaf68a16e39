package dao

import (
	"context"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"
	"transwarp.io/mlops/pipeline/internal/dao/model"
)

type TaskDAO struct {
	*CommonDAO
}

type QueryCond struct {
	Page     int32 `json:"page"`
	PageSize int32 `json:"page_size"`

	QueueName string `json:"queue_name"`

	TenantIDs []string `json:"tenant_id"`
	Status    []string `json:"status" form:"status"`
	// mult
	ProjectIDs []string `json:"project_id"`
	Types      []string `json:"type" form:"type"`
	Priorities []int32  `json:"priority" form:"priority"`

	// 时间范围
	CreatedAtStart *int64 `json:"created_at_start"`
	CreatedAtEnd   *int64 `json:"created_at_end"`

	// 搜索关键词
	SearchKeyword string `json:"search_keyword" form:"search_keyword"`

	// 排序参数
	SortBy    string `json:"sort_by" form:"sort_by"`
	SortOrder string `json:"sort_order" form:"sort_order"`
}

// ListTasksResp defines the response structure for listing tasks
type QueryResult struct {
	Items      []*model.Task `json:"tasks"`
	Total      int32         `json:"total"`
	Page       int32         `json:"page"`
	PageSize   int32         `json:"page_size"`
	TotalPages int32         `json:"total_pages"`
}

func NewTaskDAO(dao *CommonDAO) *TaskDAO {
	taskDAO := &TaskDAO{
		dao,
	}
	return taskDAO
}

func (dao *TaskDAO) CreateIfNotExists(ctx context.Context, task *model.Task) (*model.Task, error) {
	q := dao.Task
	dao.db.Session(&gorm.Session{NewDB: true}).WithContext(ctx).Where(q.ID.Eq(task.ID)).Attrs(task).FirstOrCreate(task)
	// dao.db.WithContext(ctx).Where(q.ID.Eq(task.ID)).Attrs(task).FirstOrCreate(task)
	return task, nil
}

func (dao *TaskDAO) Save(ctx context.Context, task *model.Task) (*model.Task, error) {
	q := dao.Task
	if err := q.Save(task); err != nil {
		return nil, fmt.Errorf("failed to save task: %w", err)
	}
	return task, nil
}

func (dao *TaskDAO) Get(ctx context.Context, id string) (*model.Task, error) {
	q := dao.Task
	return q.Where(q.ID.Eq(id)).First()
}

func (dao *TaskDAO) Delete(ctx context.Context, id string) error {
	q := dao.Task
	_, err := q.Where(q.ID.Eq(id)).Delete(&model.Task{})
	return err
}

func (dao *TaskDAO) Update(ctx context.Context, task *model.Task) (*model.Task, error) {
	q := dao.Task
	_, err := q.Where(dao.Task.ID.Eq(task.ID)).Updates(task)
	if err != nil {
		return nil, fmt.Errorf("failed to update task: %w", err)
	}
	return task, nil
}

// ListTasks executes complex query based on the request conditions
func (dao *TaskDAO) ListTasks(ctx context.Context, queryCond *QueryCond) (*QueryResult, error) {
	if queryCond == nil {
		return nil, fmt.Errorf("request cannot be nil")
	}

	if queryCond.Page <= 0 {
		queryCond.Page = 1
	}
	if queryCond.PageSize <= 0 {
		queryCond.PageSize = 20
	}
	if queryCond.PageSize > 1000 {
		queryCond.PageSize = 1000 // Limit max page size
	}

	q := dao.Task.WithContext(ctx)

	// Apply filters
	// Filter by queue name
	if queryCond.QueueName != "" {
		q = q.Where(dao.Task.QueueName.Eq(queryCond.QueueName))
	}

	// Filter by tenant IDs
	if len(queryCond.TenantIDs) > 0 {
		q = q.Where(dao.Task.TenantID.In(queryCond.TenantIDs...))
	}

	// Filter by status
	if len(queryCond.Status) > 0 {
		q = q.Where(dao.Task.Status.In(queryCond.Status...))
	}

	// Filter by project IDs
	if len(queryCond.ProjectIDs) > 0 {
		q = q.Where(dao.Task.ProjectID.In(queryCond.ProjectIDs...))
	}

	// Filter by types
	if len(queryCond.Types) > 0 {
		q = q.Where(dao.Task.Type.In(queryCond.Types...))
	}

	// Filter by priorities
	if len(queryCond.Priorities) > 0 {
		priorityStrings := make([]string, len(queryCond.Priorities))
		for i, p := range queryCond.Priorities {
			priorityStrings[i] = fmt.Sprintf("%d", p)
		}
		q = q.Where(dao.Task.Priority.In(priorityStrings...))
	}

	// Filter by creation time range
	if queryCond.CreatedAtStart != nil {
		startTime := time.Unix(*queryCond.CreatedAtStart, 0)
		q = q.Where(dao.Task.CreatedAt.Gte(startTime))
	}
	if queryCond.CreatedAtEnd != nil {
		endTime := time.Unix(*queryCond.CreatedAtEnd, 0)
		q = q.Where(dao.Task.CreatedAt.Lte(endTime))
	}

	// Filter by search keyword (search in name, description, creator)
	if queryCond.SearchKeyword != "" {
		keyword := "%" + strings.ToLower(queryCond.SearchKeyword) + "%"
		q = q.Where(
			dao.Task.Name.Like(keyword),
		).Or(
			dao.Task.Description.Like(keyword),
		)
	}

	// Apply sorting
	sortBy := queryCond.SortBy
	if sortBy == "" {
		sortBy = "created_at" // Default sort by creation time
	}

	sortOrder := strings.ToLower(queryCond.SortOrder)
	if sortOrder != "asc" && sortOrder != "desc" {
		sortOrder = "desc" // Default to descending order
	}

	// Get the field for sorting
	if field, ok := dao.Task.GetFieldByName(sortBy); ok {
		// Apply sorting
		if sortOrder == "asc" {
			q = q.Order(field)
		} else {
			q = q.Order(field.Desc())
		}
	} else {
		// If field not found, fallback to create_at
		field := dao.Task.CreatedAt
		if sortOrder == "asc" {
			q = q.Order(field.Asc())
		} else {
			q = q.Order(field.Desc())
		}
	}

	// Get total count
	total, err := q.Count()
	if err != nil {
		return nil, fmt.Errorf("failed to count tasks: %w", err)
	}

	// Apply pagination
	offset := (queryCond.Page - 1) * queryCond.PageSize
	tasks, err := q.Offset(int(offset)).Limit(int(queryCond.PageSize)).Find()
	if err != nil {
		return nil, fmt.Errorf("failed to find tasks: %w", err)
	}

	// Calculate total pages
	totalPages := int32((total + int64(queryCond.PageSize) - 1) / int64(queryCond.PageSize))

	return &QueryResult{
		Items:      tasks,
		Total:      int32(total),
		Page:       queryCond.Page,
		PageSize:   queryCond.PageSize,
		TotalPages: totalPages,
	}, nil
}
