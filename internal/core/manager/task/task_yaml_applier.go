package task

import (
	"context"
	"fmt"
	"strings"
	"time"

	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/meta"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/discovery"
	"k8s.io/client-go/discovery/cached/memory"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/restmapper"

	"transwarp.io/mlops/pipeline/internal/core/consts"
	"transwarp.io/mlops/pipeline/internal/core/manager/task/common"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
	"transwarp.io/mlops/pipeline/internal/util"

	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

const (
	FieldManager = "task.pipeline.llmops.transwarp.io"
)

type TaskYamlApplier struct {
	dynamicClient dynamic.Interface
	restMapper    meta.RESTMapper
}

func NewTaskYamlApplier() (*TaskYamlApplier, error) {
	config, err := util.GetKubeConfig()
	if err != nil {
		panic(fmt.Sprintf("failed to get kube config: %v", err))
	}
	dynamicClient, err := dynamic.NewForConfig(config)
	if err != nil {
		panic(fmt.Sprintf("failed to create dynamic client: %v", err))
	}
	dc, err := discovery.NewDiscoveryClientForConfig(config)
	if err != nil {
		panic(fmt.Sprintf("failed to create discovery client: %v", err))
	}
	mapper := restmapper.NewDeferredDiscoveryRESTMapper(memory.NewMemCacheClient(dc))
	return &TaskYamlApplier{
		dynamicClient: dynamicClient,
		restMapper:    mapper,
	}, nil
}

type appliedResource struct {
	Kind      string
	Name      string
	Namespace string
	Resource  schema.GroupVersionResource
	Scope     meta.RESTScopeName
}

func (t *TaskYamlApplier) ApplyFromYaml(ctx context.Context, gvk schema.GroupVersionKind, yamlManifest string) error {
	// Split the YAML manifest by '---' to handle multiple resources in one file.
	manifests := strings.Split(yamlManifest, "---")

	var applied []appliedResource
	// Loop through each resource and apply it.
	for _, manifest := range manifests {
		if strings.TrimSpace(manifest) == "" {
			continue // Skip empty parts
		}
		obj, err := t.applyFromYaml(ctx, gvk, manifest)
		if err != nil {
			// If apply fails, attempt to roll back the previously applied resources.
			zlog.SugarErrorf("Failed to apply resource, starting rollback: %v", err)
			t.rollback(ctx, applied)
			return err
		}

		applied = append(applied, *obj)
	}
	return nil
}

func (t *TaskYamlApplier) applyFromYaml(ctx context.Context, gvk schema.GroupVersionKind, yamlManifest string) (*appliedResource, error) {
	oriObj, err := common.GetObjFromYaml(gvk, yamlManifest)
	if err != nil {
		return nil, err
	}
	obj, err := common.CleanupRuntimeFieldsFromRuntimeObject(oriObj)
	if err != nil {
		return nil, err
	}
	mapping, err := t.restMapper.RESTMapping(gvk.GroupKind(), gvk.Version)
	if err != nil {
		return nil, err
	}

	gvr := util.InferGVRFromGVK(gvk)
	if err := t.validateResourceLabels(obj); err != nil {
		return nil, err
	}

	// delete if exists
	err = t.dynamicClient.Resource(gvr).Namespace(obj.GetNamespace()).Delete(ctx, obj.GetName(), metav1.DeleteOptions{})
	if err != nil && !apierrors.IsNotFound(err) {
		return nil, err
	}
	// wait to delete completion
	time.Sleep(2 * time.Second)

	var dr dynamic.ResourceInterface
	if mapping.Scope.Name() == meta.RESTScopeNameNamespace {
		dr = t.dynamicClient.Resource(mapping.Resource).Namespace(obj.GetNamespace())
	} else {
		dr = t.dynamicClient.Resource(mapping.Resource)
	}

	// Perform Server-Side Apply. This is the recommended method for declarative
	// configuration. It manages ownership and intelligently merges changes.
	// "manager" should be a unique name for your application or controller.
	// metav1.ApplyOptions{FieldManager: "go-apply-example"}
	_, err = dr.Apply(ctx, obj.GetName(), obj, metav1.ApplyOptions{FieldManager: FieldManager})
	if err != nil {
		return nil, err
	}

	zlog.SugarInfof("Successfully applied resource %s/%s", obj.GetKind(), obj.GetName())
	return &appliedResource{
		Kind:      obj.GetKind(),
		Name:      obj.GetName(),
		Namespace: obj.GetNamespace(),
		Resource:  mapping.Resource,
		Scope:     mapping.Scope.Name(),
	}, nil
}

// rollback attempts to delete the resources that were successfully applied before the error.
func (t *TaskYamlApplier) rollback(ctx context.Context, applied []appliedResource) {
	for i := len(applied) - 1; i >= 0; i-- {
		res := applied[i]

		var dr dynamic.ResourceInterface
		if res.Scope == meta.RESTScopeNameNamespace {
			dr = t.dynamicClient.Resource(res.Resource).Namespace(res.Namespace)
		} else {
			dr = t.dynamicClient.Resource(res.Resource)
		}

		zlog.SugarInfof("Attempting to roll back (delete) resource %s/%s...", res.Kind, res.Name)

		// The `Delete` operation is best-effort. It might fail if the resource no longer exists,
		// or if there are other issues. We just log the error and continue.
		if err := dr.Delete(ctx, res.Name, metav1.DeleteOptions{}); err != nil {
			zlog.SugarErrorf("Failed to delete resource %s/%s during rollback: %v", res.Kind, res.Name, err)
		} else {
			zlog.SugarInfof("Successfully deleted resource %s/%s as part of rollback.", res.Kind, res.Name)
		}
	}
}

func (t *TaskYamlApplier) validateResourceLabels(obj *unstructured.Unstructured) error {
	labels := obj.GetLabels()
	if labels == nil {
		return fmt.Errorf("resource must have labels")
	}

	if !consts.HasKueueQueueNameLabel(labels) {
		return fmt.Errorf("resource must have %s label", consts.LabelKueueQueueNameKey)
	}

	namespace := obj.GetNamespace()
	if namespace == "" {
		return fmt.Errorf("resource must have a namespace")
	}

	ns, err := client.MustGetK8sClient().NamespaceLister.Get(namespace)
	if err != nil {
		return err
	}

	nsLabels := ns.GetLabels()
	if nsLabels == nil {
		return fmt.Errorf("namespace %s must have labels", namespace)
	}

	if !util.HasLLMOpsManagedLabel(nsLabels) {
		return fmt.Errorf("namespace %s is not managed by llmops", namespace)
	}

	return nil
}
