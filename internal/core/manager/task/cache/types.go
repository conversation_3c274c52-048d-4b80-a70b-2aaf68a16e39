package cache

import (
	"sync"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"
	kueuev1beta1 "sigs.k8s.io/kueue/apis/kueue/v1beta1"

	"transwarp.io/mlops/pipeline/internal/core/manager/task/common"
	"transwarp.io/mlops/pipeline/internal/dao"
)

type TaskForest struct {
	tasks     map[string]*TaskTree
	mu        sync.Mutex
	taskIDGen *common.TaskIDGenerator
}

type TaskTree struct {
	id          string
	root        *TaskRoot
	mu          sync.Mutex
	taskBuilder *common.TaskBuilder
	taskDAO     *dao.TaskDAO
}

type TaskRoot struct {
	gvr       schema.GroupVersionResource
	refs      []metav1.Object
	workloads []*TaskWorkloadNode
}

type TaskWorkloadNode struct {
	lastTransitionTime *time.Time

	gvr      schema.GroupVersionResource
	ref      *kueuev1beta1.Workload
	children []*TaskNode
}

// deployment -> replicaset -> pod
// statefulset -> replicaset -> pod
// job -> pod
// lws -> statefulset -> replicaset -> pod
// ...
type TaskNode struct {
	lastTransitionTime *time.Time

	gvr      schema.GroupVersionResource
	children []*TaskNode
	ref      metav1.Object

	isPod bool
}
