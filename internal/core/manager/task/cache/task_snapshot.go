package cache

import (
	"context"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/tools/cache"
	kueuev1beta1 "sigs.k8s.io/kueue/apis/kueue/v1beta1"

	"transwarp.io/mlops/pipeline/internal/core/consts"
	modeltask "transwarp.io/mlops/pipeline/internal/core/model/task"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
	"transwarp.io/mlops/pipeline/internal/util"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
	ptr "transwarp.io/mlops/pipeline/pkg/util/ptr"
)

type TaskSnapshot struct {
	eventHandler *EventHandler
	eventQueue   *EventQueue

	taskForest *TaskForest
}

func NewTaskSnapshot() *TaskSnapshot {
	taskForest := NewTaskForest()
	eventQueue := NewEventQueue(10000)
	return &TaskSnapshot{
		eventHandler: NewEventHandler(2, eventQueue, taskForest),
		eventQueue:   eventQueue,

		taskForest: taskForest,
	}
}

func (ts *TaskSnapshot) DeleteTask(ctx context.Context, id string) error {
	return ts.taskForest.DeleteTask(ctx, id)
}

func (ts *TaskSnapshot) StopTask(ctx context.Context, id string) error {
	return ts.taskForest.StopTask(ctx, id)
}
func (ts *TaskSnapshot) ResumeTask(ctx context.Context, id string) error {
	return ts.taskForest.ResumeTask(ctx, id)
}

func (ts *TaskSnapshot) GetTask(ctx context.Context, id string) (*modeltask.Task, error) {
	return ts.taskForest.GetTask(ctx, id)
}

func (ts *TaskSnapshot) ListTasks(ctx context.Context) ([]*modeltask.Task, error) {
	return ts.taskForest.ListTasks(ctx)
}

func (ts *TaskSnapshot) Start(ctx context.Context) error {
	ts.eventHandler.StartEventLoop(ctx)

	if client.MustGetKueueClient().IsKueueSupported() {
		client.MustGetKueueClient().AddWorkloadEventHandler(cache.FilteringResourceEventHandler{
			FilterFunc: func(obj interface{}) bool {
				kwl := obj.(*kueuev1beta1.Workload)
				return ts.isNamespaceLLMOpsManagedBy(kwl.Namespace)
			},
			Handler: cache.ResourceEventHandlerFuncs{
				AddFunc: func(obj interface{}) {
					kwl := obj.(*kueuev1beta1.Workload)
					zlog.SugarDebugf("Kueue workload added: %s/%s", kwl.Namespace, kwl.Name)
					ts.eventQueue.Enqueue(&kueueWorkloadAddEvent{
						Workload: kwl,
						BaseEvent: BaseEvent{
							CreatedAt:   ptr.ToPtr(time.Now()),
							Retries:     0,
							LastTryTime: time.Now(),
						},
					})
				},
				UpdateFunc: func(oldObj, newObj interface{}) {
					oldkwl := oldObj.(*kueuev1beta1.Workload)
					newkwl := newObj.(*kueuev1beta1.Workload)
					if oldkwl.ResourceVersion == newkwl.ResourceVersion {
						return
					}
					ts.eventQueue.Enqueue(&kueueWorkloadUpdateEvent{
						OldWorkload: oldkwl,
						NewWorkload: newkwl,
						BaseEvent: BaseEvent{
							CreatedAt:   ptr.ToPtr(time.Now()),
							Retries:     0,
							LastTryTime: time.Now(),
						},
					})
				},
				DeleteFunc: func(obj interface{}) {
					kwl := obj.(*kueuev1beta1.Workload)
					zlog.SugarDebugf("Kueue workload deleted: %s/%s", kwl.Namespace, kwl.Name)
					ts.eventQueue.Enqueue(&kueueWorkloadDeleteEvent{
						Workload: kwl,
						BaseEvent: BaseEvent{
							CreatedAt:   ptr.ToPtr(time.Now()),
							Retries:     0,
							LastTryTime: time.Now(),
						},
					})
				},
			},
		})
	}

	client.MustGetK8sClient().AddPodWatchHandler(cache.FilteringResourceEventHandler{
		FilterFunc: func(obj interface{}) bool {
			metaObj, ok := obj.(metav1.Object)
			if !ok {
				return false
			}
			if !ts.isNamespaceLLMOpsManagedBy(metaObj.GetNamespace()) {
				return false
			}
			labels := metaObj.GetLabels()
			if consts.HasKueueManagedLabel(labels) || consts.HasKueuePodsetLabel(labels) {
				return true
			}
			return false
		},
		Handler: cache.ResourceEventHandlerFuncs{
			AddFunc: func(obj interface{}) {
				if runtimeObj, ok := obj.(runtime.Object); ok {
					runtimeObj.GetObjectKind().SetGroupVersionKind(podGVK)
					pod := runtimeObj.(*corev1.Pod)
					ts.eventQueue.Enqueue(&podAddEvent{
						Pod: pod,
						BaseEvent: BaseEvent{
							CreatedAt:   ptr.ToPtr(time.Now()),
							Retries:     0,
							LastTryTime: time.Now(),
						},
					})
				}
			},
			UpdateFunc: func(oldObj, newObj interface{}) {
				if runtimeObj, ok := oldObj.(runtime.Object); ok {
					runtimeObj.GetObjectKind().SetGroupVersionKind(podGVK)
				}
				if runtimeObj, ok := newObj.(runtime.Object); ok {
					runtimeObj.GetObjectKind().SetGroupVersionKind(podGVK)
				}
				oldPod := oldObj.(*corev1.Pod)
				newPod := newObj.(*corev1.Pod)
				if oldPod.ResourceVersion == newPod.ResourceVersion {
					return
				}
				ts.eventQueue.Enqueue(&podUpdateEvent{
					OldPod: oldPod,
					NewPod: newPod,
					BaseEvent: BaseEvent{
						CreatedAt:   ptr.ToPtr(time.Now()),
						Retries:     0,
						LastTryTime: time.Now(),
					},
				})
			},
			DeleteFunc: func(obj interface{}) {
				if runtimeObj, ok := obj.(runtime.Object); ok {
					pod := runtimeObj.(*corev1.Pod)
					ts.eventQueue.Enqueue(&podDeleteEvent{
						Pod: pod,
						BaseEvent: BaseEvent{
							CreatedAt:   ptr.ToPtr(time.Now()),
							Retries:     0,
							LastTryTime: time.Now(),
						},
					})
				}
			},
		},
	})

	zlog.SugarInfof("Init task cache successfully.")
	return nil
}

func (ts *TaskSnapshot) isNamespaceLLMOpsManagedBy(ns string) bool {
	currentNamespace := util.GetCurrentNamespace()

	nsObj, err := client.MustGetK8sClient().GetNamespace(context.Background(), ns)
	if err != nil || nsObj == nil {
		return false
	}
	if ns, ok := nsObj.Labels[consts.LabelLLMOpsManagedByKey]; ok && currentNamespace == ns {
		return true
	}
	return false
}
