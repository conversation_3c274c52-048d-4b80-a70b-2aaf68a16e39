package cache

import (
	"context"
	"fmt"
	"sort"
	"sync"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	kueuev1beta1 "sigs.k8s.io/kueue/apis/kueue/v1beta1"

	"transwarp.io/mlops/pipeline/internal/core/manager/task/common"
	modeltask "transwarp.io/mlops/pipeline/internal/core/model/task"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
	ptr "transwarp.io/mlops/pipeline/pkg/util/ptr"
)

func NewTaskForest() *TaskForest {
	return &TaskForest{
		tasks:     make(map[string]*TaskTree, 0),
		mu:        sync.Mutex{},
		taskIDGen: common.NewTaskIDGenerator(),
	}
}

// delete task root ownerreference, will delete kueue workload and pod
// kueue and pod ownerreference is task root ref
func (tf *TaskForest) DeleteTask(ctx context.Context, id string) error {
	tf.mu.Lock()
	defer tf.mu.Unlock()
	if tree, ok := tf.tasks[id]; ok {
		// delete workload ref
		root := tree.root
		for _, ref := range root.refs {
			gvr := root.gvr
			if err := client.MustGetK8sClient().Delete(ctx, ref.GetNamespace(), gvr, ref.GetName()); err != nil {
				zlog.SugarErrorf("Failed to delete %s %s: %v", gvr.Resource, ref.GetName(), err)
				return err
			}
		}
		// delete from forest, to fix task has multi status different from cache and db
		delete(tf.tasks, id)

		// for _, wln := range tree.root.workloads {
		// 	kc := client.MustGetKueueClient()
		// 	err := kc.DeleteWorkload(ctx, wln.ref.Namespace, wln.ref.Name)
		// 	if err != nil {
		// 		zlog.SugarErrorf("Failed to delete workload %s: %v", wln.ref.GetName(), err)
		// 		return err
		// 	}
		// }
	}
	return nil
}

func (tf *TaskForest) StopTask(ctx context.Context, id string) error {
	tf.mu.Lock()
	defer tf.mu.Unlock()
	if tree, ok := tf.tasks[id]; ok {
		for _, wln := range tree.root.workloads {
			ref := wln.ref.DeepCopy()
			ref.Spec.Active = ptr.ToBoolPtr(false)
			kc := client.MustGetKueueClient()
			_, err := kc.UpdateWorkload(ctx, ref)
			if err != nil {
				zlog.SugarErrorf("Failed to stop workload %s: %v", wln.ref.GetName(), err)
				return err
			}
		}
	}
	return nil
}

func (tf *TaskForest) ResumeTask(ctx context.Context, id string) error {
	tf.mu.Lock()
	defer tf.mu.Unlock()
	if tree, ok := tf.tasks[id]; ok {
		for _, wln := range tree.root.workloads {
			ref := wln.ref.DeepCopy()
			ref.Spec.Active = ptr.ToBoolPtr(true)
			kc := client.MustGetKueueClient()
			_, err := kc.UpdateWorkload(ctx, ref)
			if err != nil {
				zlog.SugarErrorf("Failed to resume workload %s: %v", wln.ref.GetName(), err)
				return err
			}
		}
	}
	return nil
}

func (tf *TaskForest) GetTask(ctx context.Context, id string) (*modeltask.Task, error) {
	tasks, err := tf.ListTasks(ctx)
	if err != nil {
		return nil, err
	}
	for _, task := range tasks {
		if task.ID == id {
			return task, nil
		}
	}
	return nil, fmt.Errorf("task not found by id: %s from cache.", id)
}

func (tf *TaskForest) ListTasks(ctx context.Context) ([]*modeltask.Task, error) {
	tasks := make([]*modeltask.Task, 0)
	for _, task := range tf.tasks {
		if t, ok := task.ToTask(); ok {
			tasks = append(tasks, t)
		} else {
			zlog.SugarWarnf("Failed to convert task tree (%s) to task: root is nil or workloads is nil or workloads is empty.", task.id)
		}
	}

	// update rank
	// first find all pending task, group by priority, then order by create time, and then update rank
	tf.assignRanksToTasks(tasks)
	return tasks, nil
}

func (tf *TaskForest) assignRanksToTasks(tasks []*modeltask.Task) {
	// Group pending tasks by ClusterQueueName
	groupedPendingTasks := make(map[string][]*modeltask.Task)

	for _, task := range tasks {
		if task.Status == modeltask.TaskStatusPending {
			// Initialize Ranking to 0 for non-pending or unranked tasks
			task.Rank = 0
			group := fmt.Sprintf("%s-%s", task.TenantID, task.QueueName)
			groupedPendingTasks[group] = append(groupedPendingTasks[group], task)
		} else {
			// For non-pending tasks, explicitly set Ranking to 0 or leave as default.
			// This ensures previously assigned ranks for completed/running tasks are reset if desired.
			task.Rank = 0
		}
	}

	for _, groupTasks := range groupedPendingTasks {
		// Sort tasks within the group based on priority and creation time
		sort.Slice(groupTasks, func(i, j int) bool {
			// Primary sort: Priority (descending, higher int means higher priority, so lower rank)
			p1 := groupTasks[i].Priority.ToInt()
			p2 := groupTasks[j].Priority.ToInt()
			if p1 != p2 {
				return p1 > p2 // Higher priority integer value comes first (lower rank)
			}

			// Secondary sort: CreatedAt (ascending, earlier time gets lower rank)
			return groupTasks[i].CreatedAt.Before(groupTasks[j].CreatedAt)
		})

		// Assign ranks based on the sorted order
		for i, task := range groupTasks {
			task.Rank = i + 1 // Ranks start from 1
		}
	}
}

// Workload
func (tf *TaskForest) AddKueueWorkload(kwl *kueuev1beta1.Workload, transitionTime *time.Time) (error, bool) {
	twln := &TaskWorkloadNode{
		lastTransitionTime: transitionTime,
		gvr:                workloadGVR,
		ref:                kwl,
		children:           make([]*TaskNode, 0),
	}

	allAncestors, err := common.FindAllAncestor(twln.ref)
	if err != nil {
		zlog.SugarErrorf("Failed to find all ancestors: %v", err)
		return err, false
	}

	if len(allAncestors) == 0 {
		return fmt.Errorf("Not to find any ancestors: %s/%s", kwl.Namespace, kwl.Name), false
	}

	// Generate TaskID using unified logic
	tf.mu.Lock()
	defer tf.mu.Unlock()
	// if find tree add it, else new tree add it
	id := tf.taskIDGen.GenerateTaskID(allAncestors[0].(runtime.Object))
	// for _, ancestor := range allAncestors {
	if tree, ok := tf.findTree(id); ok {
		zlog.SugarDebugf("Add kueue workload to existing tree: %s", allAncestors[0].GetName())
		tree.AddTaskWorkloadNode(twln)
		return nil, true
	}
	// }

	zlog.SugarDebugf("New task tree: %s because not found task tree", allAncestors[0].GetName())
	newTree := NewTaskTree(allAncestors)
	newTree.AddTaskWorkloadNode(twln)
	if newTree.root == nil || newTree.root.refs == nil || len(newTree.root.refs) == 0 {
		return nil, false
	}
	newTree.id = id
	tf.tasks[id] = newTree

	return nil, true
}
func (tf *TaskForest) UpdateKueueWorkload(oldkwl, newkwl *kueuev1beta1.Workload, transitionTime *time.Time) (error, bool) {
	oldTaskWorkloadNode := &TaskWorkloadNode{
		lastTransitionTime: transitionTime,
		gvr:                workloadGVR,
		ref:                oldkwl,
		children:           make([]*TaskNode, 0),
	}
	newTaskWorkloadNode := &TaskWorkloadNode{
		lastTransitionTime: transitionTime,
		gvr:                workloadGVR,
		ref:                newkwl,
		children:           make([]*TaskNode, 0),
	}

	allAncestors, err := common.FindAllAncestor(oldTaskWorkloadNode.ref)
	if err != nil {
		zlog.SugarErrorf("Failed to find all ancestors: %v", err)
		return err, false
	}

	// if find tree and update
	for _, ancestor := range allAncestors {
		id := tf.taskIDGen.GenerateTaskID(ancestor.(runtime.Object))
		if tree, ok := tf.findTree(id); ok {
			zlog.SugarDebugf("Update kueue workload in tree: %s", ancestor.GetName())
			if err, ok := tree.UpdateTaskWorkloadNode(oldTaskWorkloadNode, newTaskWorkloadNode); err == nil && ok {
				return nil, true
			} else {
				return err, false
			}
		}
	}
	return nil, false
}
func (tf *TaskForest) DeleteKueueWorkload(kwl *kueuev1beta1.Workload, transitionTime *time.Time) (error, bool) {
	twln := &TaskWorkloadNode{
		lastTransitionTime: transitionTime,
		gvr:                workloadGVR,
		ref:                kwl,
		children:           make([]*TaskNode, 0),
	}

	for _, tree := range tf.tasks {
		if err, ok := tree.TryDeleteTaskWorkloadNode(twln); err == nil && ok {
			// id delete kwl successful, tree if no workloads, delete it
			tf.mu.Lock()
			defer tf.mu.Unlock()
			if len(tree.root.workloads) == 0 {
				zlog.SugarInfof("Delete task tree: %s because no workloads", tree.root.refs[0].GetName())
				delete(tf.tasks, tree.id)
			}
			return nil, true
		} else if err == nil && !ok {
			continue
		} else {
			return err, false
		}
	}

	return nil, true
}

// Pod
func (tf *TaskForest) AddKubernetesPod(pod *corev1.Pod, transitionTime *time.Time) (error, bool) {
	taskNode, err := tf.buildTaskNodeFromPod(pod, transitionTime)
	if err != nil {
		return err, false
	}

	// id := string(taskNode.ref.GetUID())
	id := tf.taskIDGen.GenerateTaskID(taskNode.ref.(runtime.Object))
	if tree, ok := tf.findTree(id); ok {
		if err, ok := tree.AddTaskNode(taskNode); err == nil && ok {
			return nil, true
		} else if err != nil {
			return err, false
		} else {
			return nil, false
		}
	}
	// not found tree
	return nil, false
}
func (tf *TaskForest) UpdateKubernetesPod(oldPod, newPod *corev1.Pod, transitionTime *time.Time) (error, bool) {
	oldTaskNode, err := tf.buildTaskNodeFromPod(oldPod, transitionTime)
	if err != nil {
		return err, false
	}
	newTaskNode, err := tf.buildTaskNodeFromPod(newPod, transitionTime)
	if err != nil {
		return err, false
	}
	id := tf.taskIDGen.GenerateTaskID(oldTaskNode.ref.(runtime.Object))
	if tree, ok := tf.findTree(id); ok {
		if err, ok := tree.UpdateTaskNode(oldTaskNode, newTaskNode); err == nil && ok {
			return nil, true
		} else {
			return err, false
		}
	}
	// not found tree
	return nil, false
}
func (tf *TaskForest) DeleteKubernetesPod(pod *corev1.Pod, transitionTime *time.Time) (error, bool) {
	gvr := common.InferGVRFromObject(pod)
	node := &TaskNode{
		lastTransitionTime: transitionTime,
		gvr:                gvr,
		children:           nil,
		ref:                pod,
		isPod:              true,
	}

	for _, tree := range tf.tasks {
		if err, ok := tree.TryDeleteTaskNode(node); err == nil && ok {
			return nil, true
		} else if err == nil && !ok {
			continue
		} else {
			return err, false
		}
	}

	return nil, true
}

func (tf *TaskForest) findTree(id string) (*TaskTree, bool) {
	if tree, ok := tf.tasks[id]; ok {
		return tree, true
	}
	return nil, false
}

func (tf *TaskForest) buildTaskNodeFromPod(obj metav1.Object, transitionTime *time.Time) (*TaskNode, error) {
	// find ancestor
	// build task tree
	gvr := common.InferGVRFromObject(obj)
	node := &TaskNode{
		lastTransitionTime: transitionTime,
		gvr:                gvr,
		children:           nil,
		ref:                obj,
		isPod:              true,
	}

	namespace := obj.GetNamespace()
	for {
		ownerRefs := node.ref.GetOwnerReferences()
		if len(ownerRefs) == 0 {
			break
		}

		ownerRef := ownerRefs[0]
		name := ownerRef.Name
		kind := ownerRef.Kind

		parentGVR, err := common.InferGVRFromOwnerRef(ownerRef)
		if err != nil {
			zlog.SugarErrorf("Failed to infer GVR from owner reference: %v", err)
			return nil, err
		}

		parentObj, err := common.GetObjectFromOwnerRef(namespace, ownerRef)
		if err != nil {
			return nil, fmt.Errorf("failed to get %s %s/%s: %w", kind, namespace, name, err)
		}

		parentNode := &TaskNode{
			lastTransitionTime: transitionTime,
			gvr:                parentGVR,
			ref:                parentObj,
			children:           []*TaskNode{node},
		}
		node = parentNode
	}

	return node, nil
}
