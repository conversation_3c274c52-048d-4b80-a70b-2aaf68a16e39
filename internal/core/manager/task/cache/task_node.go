package cache

import (
	"context"
	"fmt"
	"math"
	"regexp"
	"strings"

	corev1 "k8s.io/api/core/v1"
	modeltask "transwarp.io/mlops/pipeline/internal/core/model/task"
	"transwarp.io/mlops/pipeline/internal/infrastructure/metrics"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

func (tn *TaskNode) replaceOldTaskNode(oldNode, newNode *TaskNode) bool {
	if tn.children == nil || len(tn.children) == 0 {
		return false
	}

	for i, node := range tn.children {
		if node.ref.GetUID() == oldNode.ref.GetUID() {
			if newNode.lastTransitionTime.Before(*node.lastTransitionTime) {
				zlog.SugarWarnf("Skip update pod %s because new transition time %v is before old transition time %v",
					oldNode.ref.GetName(), newNode.lastTransitionTime, node.lastTransitionTime)
				return true
			}
			tn.children[i] = newNode
			return true
		}
		if node.replaceOldTaskNode(oldNode, newNode) {
			return true
		}
	}
	return false
}

func (tn *TaskNode) deleteTaskNode(node *TaskNode) bool {
	if tn.children == nil || len(tn.children) == 0 {
		return false
	}

	for i, child := range tn.children {
		if child.ref.GetUID() == node.ref.GetUID() {
			tn.children = append(tn.children[:i], tn.children[i+1:]...)
			return true
		}
		if child.deleteTaskNode(node) {
			return true
		}
	}
	return false
}

func (tn *TaskNode) hasLeafPod() bool {
	if tn.isPod {
		return true
	}
	if tn.children == nil || len(tn.children) == 0 {
		return false
	}
	for _, child := range tn.children {
		if child.hasLeafPod() {
			return true
		}
	}
	return false
}

func (tn *TaskNode) getAllPods() ([]*modeltask.Pod, error) {
	pods := make([]*modeltask.Pod, 0)
	if tn.isPod {
		p := tn.ref.(*corev1.Pod)
		pod := &modeltask.Pod{
			Name:      p.GetName(),
			Namespace: p.GetNamespace(),
			NodeName:  p.Spec.NodeName,
			Phase:     p.Status.Phase,
			HostIP:    p.Status.HostIP,
		}
		if p.Status.StartTime != nil {
			pod.StartedAt = &p.Status.StartTime.Time
		}

		if p.Status.Conditions != nil && len(p.Status.Conditions) > 0 {
			for _, condition := range p.Status.Conditions {
				if condition.Type == corev1.PodScheduled {
					pod.ScheduledAt = &condition.LastTransitionTime.Time
				}
			}
		}

		containers := make([]*modeltask.Container, 0)
		for _, c := range p.Spec.Containers {
			container := &modeltask.Container{
				Name:  c.Name,
				Image: c.Image,
			}
			container.Resources = tn.getContainerResources(c)
			containers = append(containers, container)
		}
		pod.Containers = containers

		totalResources := &modeltask.Resources{
			Requests: *modeltask.NewResourceSpec(),
			Limits:   *modeltask.NewResourceSpec(),
			Used:     modeltask.NewResourceSpec(),
		}
		for _, c := range pod.Containers {
			totalResources.Add(c.Resources)
		}

		podUsage, err := metrics.MustGetMetricMonitor().QueryPodResourceUsage(context.Background(), pod.Namespace, pod.Name)
		if err != nil {
			zlog.SugarWarnf("Failed to query pod(%s) %s metric: %v", pod.Phase, pod.Name, err)
			totalResources.Used = modeltask.NewResourceSpec()
		} else {
			totalResources.Used = &modeltask.ResourceSpec{
				CPU:    fmt.Sprintf("%dm", int(math.Ceil(podUsage.CPUUsage))),
				Memory: fmt.Sprintf("%dMi", int(math.Ceil(podUsage.MemoryUsage))),
				XPU:    []*modeltask.XPUResource{},
			}

			if podUsage.DeviceType != "" {
				totalResources.Used.XPU = append(totalResources.Used.XPU, &modeltask.XPUResource{
					Name: podUsage.DeviceType,
					// TODO some xpu is 1/16 or other
					Count:  podUsage.XPUUsage / 100.0,
					Cores:  int64(math.Ceil(podUsage.XPUUsage)),
					Memory: fmt.Sprintf("%dMi", int(math.Ceil(podUsage.XPUMemoryUsage))),
				})
			}
		}

		pod.Resources = totalResources

		pods = append(pods, pod)
	} else if tn.children != nil && len(tn.children) > 0 {
		for _, node := range tn.children {
			childPods, err := node.getAllPods()
			if err != nil {
				return nil, err
			}
			pods = append(pods, childPods...)
		}
	}
	return pods, nil
}

func (tn *TaskNode) getContainerResources(container corev1.Container) *modeltask.Resources {
	vendor := func(key string) (string, string) {
		re := regexp.MustCompile(`^(cores|mem)\..*\/(.*)$`)
		if matches := re.FindStringSubmatch(key); len(matches) > 2 {
			return matches[2], matches[1]
		}
		return "", ""
	}
	requestsXPU := make(map[string]*modeltask.XPUResource)
	for key, quantity := range container.Resources.Requests {
		vendor, itemType := vendor(string(key))
		if vendor == "" || itemType == "" {
			continue
		}
		if _, ok := requestsXPU[vendor]; !ok {
			requestsXPU[vendor] = &modeltask.XPUResource{
				Name:   vendor,
				Count:  0,
				Cores:  0,
				Memory: "0Mi",
			}
		}
		if strings.Contains(itemType, "mem") {
			requestsXPU[vendor].Memory = quantity.String()
		} else if strings.Contains(itemType, "core") {
			requestsXPU[vendor].Cores = quantity.Value()
		}
	}

	limitsXPU := make(map[string]*modeltask.XPUResource)
	for key, quantity := range container.Resources.Requests {
		vendor, itemType := vendor(string(key))
		if vendor == "" || itemType == "" {
			continue
		}
		if _, ok := limitsXPU[vendor]; !ok {
			limitsXPU[vendor] = &modeltask.XPUResource{
				Name:   vendor,
				Count:  0,
				Cores:  0,
				Memory: "0Mi",
			}
		}
		if strings.Contains(itemType, "mem") {
			limitsXPU[vendor].Memory = quantity.String()
		} else if strings.Contains(itemType, "core") {
			limitsXPU[vendor].Cores = quantity.Value()
		}
	}

	reqXPUs := make([]*modeltask.XPUResource, 0)
	for _, xpu := range requestsXPU {
		if xpu.Cores != 0 {
			xpu.Count = float64(xpu.Cores) / 100.0
		}
		reqXPUs = append(reqXPUs, xpu)
	}
	limitXPUs := make([]*modeltask.XPUResource, 0)
	for _, xpu := range limitsXPU {
		if xpu.Cores != 0 {
			xpu.Count = float64(xpu.Cores) / 100.0
		}
		limitXPUs = append(limitXPUs, xpu)
	}

	return &modeltask.Resources{
		Requests: modeltask.ResourceSpec{
			CPU:    container.Resources.Requests.Cpu().String(),
			Memory: container.Resources.Requests.Memory().String(),
			XPU:    reqXPUs,
		},
		Limits: modeltask.ResourceSpec{
			CPU:    container.Resources.Limits.Cpu().String(),
			Memory: container.Resources.Limits.Memory().String(),
			XPU:    limitXPUs,
		},
		Used: &modeltask.ResourceSpec{
			CPU:    "0",
			Memory: "0",
			XPU:    make([]*modeltask.XPUResource, 0),
		},
	}
}
