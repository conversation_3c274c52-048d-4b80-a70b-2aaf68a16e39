package task

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"gopkg.in/yaml.v3"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/client-go/dynamic"

	"transwarp.io/mlops/pipeline/internal/core/manager/task/common"
	"transwarp.io/mlops/pipeline/internal/core/manager/task/query"
	model "transwarp.io/mlops/pipeline/internal/core/model/task"
	"transwarp.io/mlops/pipeline/internal/dao"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
	util "transwarp.io/mlops/pipeline/internal/util"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
	ptr "transwarp.io/mlops/pipeline/pkg/util/ptr"
)

type TaskManager struct {
	queryExecutor *query.QueryExecutor[*model.Task]
	dataProvider  *TaskDataProvider
	dynamicClient dynamic.Interface

	taskYamlApplier *TaskYamlApplier

	mu sync.Mutex
}

func NewTaskManager() *TaskManager {
	kueueClient := client.MustGetKueueClient()
	dataProvider := NewTaskDataProvider(kueueClient)

	config, err := util.GetKubeConfig()
	if err != nil {
		panic(fmt.Sprintf("failed to get kube config: %v", err))
	}
	dynamicClient, err := dynamic.NewForConfig(config)
	if err != nil {
		panic(fmt.Sprintf("failed to create dynamic client: %v", err))
	}

	executorConfig := &query.ExecutorConfig{
		QueryTimeout:       300 * time.Second,
		SlowQueryThreshold: 10 * time.Second,
		MaxConcurrency:     20,
	}
	queryExecutor := query.NewQueryExecutor[*model.Task](dataProvider, executorConfig)

	tya, err := NewTaskYamlApplier()
	if err != nil {
		panic(fmt.Sprintf("failed to create task yaml applier: %v", err))
	}
	return &TaskManager{
		queryExecutor: queryExecutor,
		dataProvider:  dataProvider,
		dynamicClient: dynamicClient,

		taskYamlApplier: tya,
		mu:              sync.Mutex{},
	}
}

func (tm *TaskManager) ListTasks(ctx context.Context, req *model.ListTasksReq) (*model.ListTasksResp, error) {
	zlog.Info(fmt.Sprintf("Listing tasks with request: %+v", req))

	if tm.OnlyQueryCache(req.Status) {
		queryBuilder := query.NewTaskQuery()
		tm.applyFilters(queryBuilder, req)
		tm.applySorting(queryBuilder, req)
		if req.Page > 0 && req.PageSize > 0 {
			queryBuilder.Limit(req.Page, req.PageSize)
		}

		var result *query.QueryResult[*model.Task]
		var err error

		result, err = tm.queryExecutor.Execute(ctx, queryBuilder.QueryBuilder)
		if err != nil {
			return nil, fmt.Errorf("failed to execute task query: %w", err)
		}
		if err := tm.fillProjectAndTenantName(ctx, result.Items); err != nil {
			zlog.SugarErrorf("Failed to fill project and tenant name: %v", err)
		}

		return &model.ListTasksResp{
			Items:      result.Items,
			Total:      result.Total,
			Page:       result.Page,
			PageSize:   result.PageSize,
			TotalPages: result.TotalPages,
		}, nil
	} else {
		queryCond := &dao.QueryCond{
			Page:     req.Page,
			PageSize: req.PageSize,

			QueueName: req.QueueName,

			TenantIDs:  req.TenantIDs,
			Status:     req.Status,
			ProjectIDs: req.ProjectIDs,
			Types:      req.Types,
			Priorities: req.Priorities,

			CreatedAtStart: req.CreatedAtStart,
			CreatedAtEnd:   req.CreatedAtEnd,

			SearchKeyword: req.SearchKeyword,

			SortBy:    req.SortBy,
			SortOrder: req.SortOrder,
		}
		result, err := dao.MustGetTaskDAO().ListTasks(ctx, queryCond)
		if err != nil {
			return nil, err
		}
		cacheTasks, err := tm.listTasksFromCache(ctx)
		if err != nil {
			return nil, err
		}
		items := make([]*model.Task, len(result.Items))
		for i, daoTask := range result.Items {
			for _, cacheTask := range cacheTasks {
				if cacheTask.ID == daoTask.ID {
					items[i] = cacheTask
					break
				}
			}
			if items[i] == nil {
				items[i], err = common.FromDAOModel(daoTask)
				if err != nil {
					return nil, err
				}
			}
		}
		if err := tm.fillProjectAndTenantName(ctx, items); err != nil {
			zlog.SugarErrorf("Failed to fill project and tenant name: %v", err)
		}

		return &model.ListTasksResp{
			Items:      items,
			Total:      result.Total,
			Page:       result.Page,
			PageSize:   result.PageSize,
			TotalPages: result.TotalPages,
		}, nil
	}
}

func (tm *TaskManager) fillProjectAndTenantName(ctx context.Context, tasks []*model.Task) error {
	casHttpClient, err := client.GetCASHTTPClient()
	if err != nil {
		return err
	}
	for _, task := range tasks {
		task.ProjectName = casHttpClient.GetProjectName(task.ProjectID)
		task.TenantName = casHttpClient.GetTenantName(task.TenantID)
	}
	return nil
}

func (tm *TaskManager) listTasksFromDB(ctx context.Context) ([]*model.Task, error) {
	return tm.dataProvider.GetData(ctx)
}

func (tm *TaskManager) listTasksFromCache(ctx context.Context) ([]*model.Task, error) {
	return tm.dataProvider.GetData(ctx)
}

// first delete from queue, then delete from msyql(task meta)
func (tm *TaskManager) DeleteTask(ctx context.Context, id string) error {
	// delete from queue
	if err := tm.dataProvider.DeleteTask(ctx, id); err != nil {
		return err
	}
	// delete from mysql
	if err := dao.MustGetTaskDAO().Delete(ctx, id); err != nil {
		return err
	}
	return nil
}

func (tm *TaskManager) GetTask(ctx context.Context, id string) (*model.Task, error) {
	// find from cache first, if not found then find from db
	task, err := tm.dataProvider.GetTask(ctx, id)
	if err != nil {
		zlog.SugarErrorf("Failed to get task %s from cache, error: %v", id, err)
	} else {
		return task, nil
	}

	daotask, err := dao.MustGetTaskDAO().Get(ctx, id)
	if err != nil {
		return nil, err
	}
	return common.FromDAOModel(daotask)
}

func (tm *TaskManager) OnlyQueryCache(statuses []string) bool {
	if statuses != nil && len(statuses) > 0 {
		for _, status := range statuses {
			s := model.TaskStatus(status)
			if s == model.TaskStatusSucceeded || s == model.TaskStatusFailed || s == model.TaskStatusCancelled {
				return false
			}
		}
		return true
	}
	return false
}

func (ts *TaskManager) applyFilters(queryBuilder *query.TaskQuery, req *model.ListTasksReq) {
	if len(req.ProjectIDs) > 0 {
		queryBuilder.WhereProjectIDs(req.ProjectIDs)
	}
	if len(req.TenantIDs) > 0 {
		queryBuilder.WhereTenantIDs(req.TenantIDs)
	}

	if len(req.Status) > 0 {
		statuses := make([]model.TaskStatus, len(req.Status))
		for i, s := range req.Status {
			statuses[i] = model.TaskStatus(s)
		}
		queryBuilder.WhereStatuses(statuses)
	}

	if len(req.Types) > 0 {
		types := make([]model.TaskType, len(req.Types))
		for i, t := range req.Types {
			types[i] = model.TaskType(t)
		}
		queryBuilder.WhereTypes(types)
	}

	if len(req.Priorities) > 0 {
		priorities := make([]model.TaskPriority, len(req.Priorities))
		for i, p := range req.Priorities {
			priorities[i] = model.TaskPriority(p)
		}
		queryBuilder.WherePriorities(priorities)
	}

	if req.CreatedAtStart != nil {
		startTime := time.Unix(*req.CreatedAtStart, 0)
		queryBuilder.WhereCreatedAfter(startTime)
	}

	if req.CreatedAtEnd != nil {
		endTime := time.Unix(*req.CreatedAtEnd, 0)
		queryBuilder.WhereCreatedBefore(endTime)
	}

	if req.SearchKeyword != "" {
		queryBuilder.WhereKeywordSearch(req.SearchKeyword)
	}

	if req.QueueName != "" {
		queryBuilder.WhereQueueName(req.QueueName)
	}
}

func (ts *TaskManager) applySorting(queryBuilder *query.TaskQuery, req *model.ListTasksReq) {
	sortBy := req.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}

	sortOrder := req.SortOrder
	if sortOrder == "" {
		sortOrder = "desc"
	}

	direction := query.SortAsc
	if sortOrder == "desc" {
		direction = query.SortDesc
	}

	switch strings.ToLower(sortBy) {
	case "name":
		queryBuilder.OrderByName(direction)
	case "created_at":
		queryBuilder.OrderByCreatedAt(direction)
	case "started_at":
		queryBuilder.OrderByStartedAt(direction)
	case "creator":
		queryBuilder.OrderByStatus(direction)
	case "priority":
		queryBuilder.OrderByPriority(direction)
	case "status":
		queryBuilder.OrderByStatus(direction)
	case "queued_duration":
		queryBuilder.OrderByQueuedDuration(direction)
	case "pod_count":
		queryBuilder.OrderByPodCount(direction)
	default:
		queryBuilder.OrderByCreatedAt(direction)
	}
}

func (tm *TaskManager) CreateTaskFromYAML(ctx context.Context, yamlManifest string) (*model.Task, error) {
	var obj unstructured.Unstructured
	if err := yaml.Unmarshal([]byte(yamlManifest), &obj); err != nil {
		return nil, err
	}

	// TODO
	gvk := obj.GroupVersionKind()
	err := tm.taskYamlApplier.ApplyFromYaml(ctx, gvk, yamlManifest)
	if err != nil {
		return nil, err
	}
	zlog.SugarInfof("Successfully created task %s from YAML")
	return nil, nil
}

// delete from kueue
// update task status to cancelled
func (ts *TaskManager) CancelTask(ctx context.Context, id string) error {
	ts.mu.Lock()
	defer ts.mu.Unlock()

	task, err := ts.GetTask(ctx, id)
	if err != nil {
		return err
	}
	if err := ts.dataProvider.DeleteTask(ctx, id); err != nil {
		return err
	}
	task.EndedAt = ptr.ToPtr(time.Now())
	task.Status = model.TaskStatusCancelled
	daoTask, err := common.ToDAOModel(task)
	if err != nil {
		return err
	}
	_, err = dao.MustGetTaskDAO().Save(ctx, daoTask)
	if err != nil {
		return err
	}
	return nil
}

// stop running, pending in queue
func (ts *TaskManager) StopTask(ctx context.Context, taskID string) error {
	ts.mu.Lock()
	defer ts.mu.Unlock()
	return ts.dataProvider.StopTask(ctx, taskID)
}

func (ts *TaskManager) ResumeTask(ctx context.Context, taskID string) error {
	ts.mu.Lock()
	defer ts.mu.Unlock()
	return ts.dataProvider.ResumeTask(ctx, taskID)
}

func (tm *TaskManager) RerunTask(ctx context.Context, taskID string) (*model.Task, error) {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	originalTask, err := tm.GetTask(ctx, taskID)
	if err != nil {
		return nil, err
	}
	if !tm.CanRerunTask(originalTask) {
		return nil, fmt.Errorf("task %s cannot be rerun, current status: %s", taskID, originalTask.Status)
	}
	// delete from cache first
	if err := tm.dataProvider.DeleteTask(ctx, taskID); err != nil {
		return nil, fmt.Errorf("failed to cancel task %s before rerun: %v", taskID, err)
	}

	// if argo workflow task, call argo retry api
	if client.MustGetArgoWorkflowClient().IsArgoWorkflowTask(originalTask.GVK) {
		if err := client.MustGetArgoWorkflowClient().RetryWorkflowFromYaml(originalTask.YamlRawManifest); err != nil {
			return nil, err
		}
	} else {
		err = tm.taskYamlApplier.ApplyFromYaml(ctx, originalTask.GVK, originalTask.YamlRawManifest)
		if err != nil {
			return nil, err
		}
	}

	zlog.SugarInfof("Successfully rerun task %s", taskID)
	// TODO wait can get task from cache ?
	originalTask.Status = model.TaskStatusPending
	return originalTask, nil
}

func (ts *TaskManager) canRerunTask(task *model.Task) bool {
	switch task.Status {
	case model.TaskStatusRunning, model.TaskStatusPending, model.TaskStatusSucceeded:
		return false
	case model.TaskStatusFailed, model.TaskStatusCancelled:
		return true
	default:
		return false
	}
}

func (ts *TaskManager) CanRerunTask(task *model.Task) bool {
	return ts.canRerunTask(task)
}
