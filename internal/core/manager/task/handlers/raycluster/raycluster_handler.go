package raycluster

import (
	"fmt"

	rayv1 "github.com/ray-project/kuberay/ray-operator/apis/ray/v1"
	"k8s.io/apimachinery/pkg/runtime"

	framework "transwarp.io/mlops/pipeline/internal/core/manager/task/handlerframework"
	model "transwarp.io/mlops/pipeline/internal/core/model/task"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

var (
	gvk = rayv1.GroupVersion.WithKind("RayCluster")
)

const (
	headGroupPodSetName = "head"
	FrameworkName       = "ray.io/raycluster"
)

type RayClusterHandler struct {
	*framework.BaseHandler
	generator framework.TaskGenerator
}

func init() {
	if client.MustGetRayClient().IsRaySupported() {

		handler := NewRayClusterHandler(
			framework.NewBaseHandler(
				client.MustGetRayClient().GetRayClusterInformer(),
			),
			framework.NewDefaultTaskGenerator(),
		)

		framework.RegisterHandler(FrameworkName, handler)
	}
}

func NewRayClusterHandler(baseHandler *framework.BaseHandler, generator framework.TaskGenerator) *RayClusterHandler {
	return &RayClusterHandler{
		BaseHandler: baseHandler,
		generator:   generator,
	}
}

func (h *RayClusterHandler) Name() string {
	return FrameworkName
}

func (h *RayClusterHandler) HandleResourceCreate(obj runtime.Object) {
	task, err := h.Handle(obj)
	if err != nil {
		zlog.SugarErrorf("Failed to handle task creation: %v", err)
	} else {
		zlog.SugarDebugf("Succeeded to handle task creation, task id: %s, task_name: %s", task.ID, task.Name)
	}
}

func (h *RayClusterHandler) Handle(obj runtime.Object) (*model.Task, error) {
	rayCluster, ok := obj.(*rayv1.RayCluster)
	if !ok {
		return nil, fmt.Errorf("object is not a RayCluster")
	}

	task, err := h.generator.GenerateTask(rayCluster)
	if err != nil {
		return nil, fmt.Errorf("failed to extract RayCluster task info: %w", err)
	}

	err = h.generator.SaveTask(task)
	if err != nil {
		return nil, fmt.Errorf("failed to save task: %w", err)
	}

	return task, nil
}
