package rayjob

import (
	"fmt"

	rayv1 "github.com/ray-project/kuberay/ray-operator/apis/ray/v1"

	"k8s.io/apimachinery/pkg/runtime"
	model "transwarp.io/mlops/pipeline/internal/core/model/task"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"

	framework "transwarp.io/mlops/pipeline/internal/core/manager/task/handlerframework"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

var (
	gvk = rayv1.GroupVersion.WithKind("RayJob")
)

const (
	headGroupPodSetName    = "head"
	submitterJobPodSetName = "submitter"
	FrameworkName          = "ray.io/rayjob"
)

type RayJobHandler struct {
	*framework.BaseHandler
	generator framework.TaskGenerator
}

func init() {
	if client.MustGetRayClient().IsRaySupported() {
		handler := NewRayJobHandler(
			framework.NewBaseHandler(
				client.MustGetRayClient().GetRayJobInformer(),
			),
			framework.NewDefaultTaskGenerator(),
		)

		framework.RegisterHandler(FrameworkName, handler)
	}
}

func NewRayJobHandler(baseHandler *framework.BaseHandler, generator framework.TaskGenerator) *RayJobHandler {
	return &RayJobHandler{
		BaseHandler: baseHandler,
		generator:   generator,
	}
}

func (h *RayJobHandler) Name() string {
	return FrameworkName
}

func (h *RayJobHandler) HandleResourceCreate(obj runtime.Object) {
	task, err := h.Handle(obj)
	if err != nil {
		zlog.SugarErrorf("Failed to handle task creation: %v", err)
	} else {
		zlog.SugarDebugf("Succeeded to handle task creation, task id: %s, task_name: %s", task.ID, task.Name)
	}
}

func (h *RayJobHandler) Handle(obj runtime.Object) (*model.Task, error) {
	rayJob, ok := obj.(*rayv1.RayJob)
	if !ok {
		return nil, fmt.Errorf("object is not a RayJob")
	}

	task, err := h.generator.GenerateTask(rayJob)
	if err != nil {
		return nil, fmt.Errorf("failed to extract RayJob task info: %w", err)
	}
	err = h.generator.SaveTask(task)
	if err != nil {
		return nil, fmt.Errorf("failed to save task: %w", err)
	}

	return task, nil
}
