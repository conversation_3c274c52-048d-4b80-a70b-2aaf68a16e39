package appwrapper

import (
	"fmt"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	awv1beta2 "github.com/project-codeflare/appwrapper/api/v1beta2"
	"k8s.io/apimachinery/pkg/runtime"
	model "transwarp.io/mlops/pipeline/internal/core/model/task"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"

	framework "transwarp.io/mlops/pipeline/internal/core/manager/task/handlerframework"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

var (
	gvk = awv1beta2.GroupVersion.WithKind(awv1beta2.AppWrapperKind)

	FrameworkName = "workload.codeflare.dev/appwrapper"
)

type AppWrapperHandler struct {
	*framework.BaseHandler
	generator framework.TaskGenerator
}

func init() {
	if client.MustGetAppWrapperClient().IsAppWrapperSupported(){
		handler := NewAppWrapperHandler(
			framework.NewBaseHandler(
				client.MustGetAppWrapperClient().GetAppWrapperInformer(),
			),
			framework.NewDefaultTaskGenerator(),
		)
		framework.RegisterHandler(FrameworkName, handler)
	}
}

func NewAppWrapperHandler(baseHandler *framework.BaseHandler, generator framework.TaskGenerator) *AppWrapperHandler {
	return &AppWrapperHandler{
		BaseHandler: baseHandler,
		generator:   generator,
	}
}

func (h *AppWrapperHandler) Name() string {
	return FrameworkName
}

func (h *AppWrapperHandler) HandleResourceCreate(obj runtime.Object) {
	task, err := h.Handle(obj)
	if err != nil {
		zlog.SugarErrorf("Failed to handle task creation: %v", err)
	} else {
		zlog.SugarDebugf("Succeeded to handle task creation, task id: %s, task_name: %s", task.ID, task.Name)
	}
}

func (h *AppWrapperHandler) Handle(obj runtime.Object) (*model.Task, error) {
	// appwrapper, ok := obj.(*awv1beta2.AppWrapper)
	metaObj, ok := obj.(metav1.Object)
	if !ok {
		return nil, fmt.Errorf("object is not a Metav1 Object")
	}
	appwrapper, err := client.MustGetAppWrapperClient().GetAppWrapper(metaObj.GetNamespace(), metaObj.GetName())
	if err != nil {
		return nil, fmt.Errorf("object is not a AppWrapper")
	}

	task, err := h.generator.GenerateTask(appwrapper)
	if err != nil {
		return nil, fmt.Errorf("failed to extract appwrapper task info: %w", err)
	}

	err = h.generator.SaveTask(task)
	if err != nil {
		return nil, fmt.Errorf("failed to save task: %w", err)
	}

	return task, nil
}
