package rayservice

import (
	"fmt"

	rayv1 "github.com/ray-project/kuberay/ray-operator/apis/ray/v1"
	"k8s.io/apimachinery/pkg/runtime"

	framework "transwarp.io/mlops/pipeline/internal/core/manager/task/handlerframework"
	model "transwarp.io/mlops/pipeline/internal/core/model/task"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

var (
	gvk = rayv1.GroupVersion.WithKind("RayService")
)

const (
	headGroupPodSetName = "head"
	FrameworkName       = "ray.io/rayservice"
)

type RayServiceHandler struct {
	*framework.BaseHandler
	generator framework.TaskGenerator
}

func init() {
	if client.MustGetRayClient().IsRaySupported() {
		handler := NewRayServiceHandler(
			framework.NewBaseHandler(
				client.MustGetRayClient().GetRayServiceInformer(),
			),
			framework.NewDefaultTaskGenerator(),
		)
		framework.RegisterHandler(FrameworkName, handler)
	}
}

func NewRayServiceHandler(baseHandler *framework.BaseHandler, generator framework.TaskGenerator) *RayServiceHandler {
	return &RayServiceHandler{
		BaseHandler: baseHandler,
		generator:   generator,
	}
}

func (h *RayServiceHandler) Name() string {
	return FrameworkName
}

func (h *RayServiceHandler) HandleResourceCreate(obj runtime.Object) {
	task, err := h.Handle(obj)
	if err != nil {
		zlog.SugarErrorf("Failed to handle task creation: %v", err)
	} else {
		zlog.SugarDebugf("Succeeded to handle task creation, task id: %s, task_name: %s", task.ID, task.Name)
	}
}

func (h *RayServiceHandler) Handle(obj runtime.Object) (*model.Task, error) {
	rayService, ok := obj.(*rayv1.RayService)
	if !ok {
		return nil, fmt.Errorf("object is not a RayService")
	}

	task, err := h.generator.GenerateTask(rayService)
	if err != nil {
		return nil, fmt.Errorf("failed to extract RayService task info: %w", err)
	}

	err = h.generator.SaveTask(task)
	if err != nil {
		return nil, fmt.Errorf("failed to save task: %w", err)
	}

	return task, nil
}
