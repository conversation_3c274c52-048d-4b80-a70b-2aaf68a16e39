package job

import (
	"fmt"

	zlog "transwarp.io/mlops/pipeline/pkg/util/log"

	batchv1 "k8s.io/api/batch/v1"
	"k8s.io/apimachinery/pkg/runtime"
	framework "transwarp.io/mlops/pipeline/internal/core/manager/task/handlerframework"
	model "transwarp.io/mlops/pipeline/internal/core/model/task"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
)

var (
	gvk = batchv1.SchemeGroupVersion.WithKind("Job")

	FrameworkName = "batch/job"
)

type JobHandler struct {
	*framework.BaseHandler
	generator framework.TaskGenerator
}

func init() {
	handler := NewJobHandler(
		framework.NewBaseHandler(
			client.MustGetK8sClient().JobInformer,
		),
		framework.NewDefaultTaskGenerator(),
	)

	framework.RegisterHandler(FrameworkName, handler)
}

func NewJobHandler(baseHandler *framework.BaseHandler, generator framework.TaskGenerator) *JobHandler {
	return &JobHandler{
		BaseHandler: baseHandler,
		generator:   generator,
	}
}

func (h *JobHandler) Name() string {
	return FrameworkName
}

func (h *JobHandler) HandleResourceCreate(obj runtime.Object) {
	task, err := h.Handle(obj)
	if err != nil {
		zlog.SugarErrorf("Failed to handle task creation: %v", err)
	} else {
		zlog.SugarDebugf("Succeeded to handle task creation, task id: %s, task_name: %s", task.ID, task.Name)
	}
}
func (h *JobHandler) Handle(obj runtime.Object) (*model.Task, error) {
	job, ok := obj.(*batchv1.Job)
	if !ok {
		return nil, fmt.Errorf("object is not a Job")
	}

	task, err := h.generator.GenerateTask(job)
	if err != nil {
		return nil, fmt.Errorf("failed to extract job task info: %w", err)
	}

	err = h.generator.SaveTask(task)
	if err != nil {
		return nil, fmt.Errorf("failed to save task: %w", err)
	}

	return task, nil
}
