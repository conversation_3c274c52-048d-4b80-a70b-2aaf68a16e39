package jobset

import (
	"fmt"

	"k8s.io/apimachinery/pkg/runtime"
	jobsetapiv1alpha2 "sigs.k8s.io/jobset/api/jobset/v1alpha2"

	framework "transwarp.io/mlops/pipeline/internal/core/manager/task/handlerframework"
	model "transwarp.io/mlops/pipeline/internal/core/model/task"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"

	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

var (
	gvk = jobsetapiv1alpha2.GroupVersion.WithKind("JobSet")

	FrameworkName = "jobset.x-k8s.io/jobset"
)

type JobSetSetHandler struct {
	*framework.BaseHandler
	generator framework.TaskGenerator
}

func init() {
	if client.MustGetJobSetClient().IsJobSetSupported() {
		handler := NewJobSetHandler(
			framework.NewBaseHandler(
				client.MustGetJobSetClient().GetJobSetInformer(),
			),
			framework.NewDefaultTaskGenerator(),
		)

		framework.RegisterHandler(FrameworkName, handler)
	}
}

func NewJobSetHandler(baseHandler *framework.BaseHandler, generator framework.TaskGenerator) *JobSetSetHandler {
	return &JobSetSetHandler{
		BaseHandler: baseHandler,
		generator:   generator,
	}
}

func (h *JobSetSetHandler) Name() string {
	return FrameworkName
}

func (h *JobSetSetHandler) HandleResourceCreate(obj runtime.Object) {
	task, err := h.Handle(obj)
	if err != nil {
		zlog.SugarErrorf("Failed to handle task creation: %v", err)
	} else {
		zlog.SugarDebugf("Succeeded to handle task creation, task id: %s, task_name: %s", task.ID, task.Name)
	}
}
func (h *JobSetSetHandler) Handle(obj runtime.Object) (*model.Task, error) {
	jobset, ok := obj.(*jobsetapiv1alpha2.JobSet)
	if !ok {
		return nil, fmt.Errorf("object is not a JobSet")
	}

	task, err := h.generator.GenerateTask(jobset)
	if err != nil {
		return nil, fmt.Errorf("failed to extract jobset's task info: %w", err)
	}

	err = h.generator.SaveTask(task)
	if err != nil {
		return nil, fmt.Errorf("failed to save jobset's task: %w", err)
	}

	return task, nil
}
