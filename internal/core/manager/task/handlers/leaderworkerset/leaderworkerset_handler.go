package leaderworkerset

import (
	"fmt"

	zlog "transwarp.io/mlops/pipeline/pkg/util/log"

	"k8s.io/apimachinery/pkg/runtime"
	leaderworkersetv1 "sigs.k8s.io/lws/api/leaderworkerset/v1"
	framework "transwarp.io/mlops/pipeline/internal/core/manager/task/handlerframework"
	model "transwarp.io/mlops/pipeline/internal/core/model/task"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
)

var (
	gvk = leaderworkersetv1.SchemeGroupVersion.WithKind("LeaderWorkerSet")
)

const (
	FrameworkName = "leaderworkerset.x-k8s.io/leaderworkerset"
)

type LeaderWorkerSetHandler struct {
	*framework.BaseHandler
	generator framework.TaskGenerator
}

func init() {
	if client.MustGetLeaderWorkerSetClient().IsLWSSupported(){
		handler := NewLeaderWorkerSetHandler(
			framework.NewBaseHandler(
				client.MustGetLeaderWorkerSetClient().LeaderWorkerSetInformer,
			),
			framework.NewDefaultTaskGenerator(),
		)

		framework.RegisterHandler(FrameworkName, handler)
	}
}

func NewLeaderWorkerSetHandler(baseHandler *framework.BaseHandler, generator framework.TaskGenerator) *LeaderWorkerSetHandler {
	return &LeaderWorkerSetHandler{
		BaseHandler: baseHandler,
		generator:   generator,
	}
}

func (h *LeaderWorkerSetHandler) Name() string {
	return FrameworkName
}

func (h *LeaderWorkerSetHandler) HandleResourceCreate(obj runtime.Object) {
	task, err := h.Handle(obj)
	if err != nil {
		zlog.SugarErrorf("Failed to handle task creation: %v", err)
	} else {
		zlog.SugarDebugf("Succeeded to handle task creation, task id: %s, task_name: %s", task.ID, task.Name)
	}
}
func (h *LeaderWorkerSetHandler) Handle(obj runtime.Object) (*model.Task, error) {
	lws, ok := obj.(*leaderworkersetv1.LeaderWorkerSet)
	if !ok {
		return nil, fmt.Errorf("object is not a LeaderWorkerSet")
	}

	task, err := h.generator.GenerateTask(lws)
	if err != nil {
		return nil, fmt.Errorf("failed to extract lws task info: %w", err)
	}

	err = h.generator.SaveTask(task)
	if err != nil {
		return nil, fmt.Errorf("failed to save task: %w", err)
	}

	return task, nil
}
