package pod

import (
	"fmt"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"transwarp.io/mlops/pipeline/internal/core/consts"
	framework "transwarp.io/mlops/pipeline/internal/core/manager/task/handlerframework"
	model "transwarp.io/mlops/pipeline/internal/core/model/task"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

// if plain pod job,
// with two labels: kueue.x-k8s.io/pod-group-name, kueue.x-k8s.io/queue-name
// and one annotation: kueue.x-k8s.io/pod-group-total-count
var (
	gvk = corev1.SchemeGroupVersion.WithKind("Pod")
)

const (
	FrameworkName = "pod"
)

type PodHandler struct {
	*framework.BaseHandler
	generator framework.TaskGenerator
}

func init() {
	handler := NewPodHandler(
		framework.NewBaseHandler(
			client.MustGetK8sClient().PodInformer,
		),
		framework.NewDefaultTaskGenerator(),
	)

	framework.RegisterHandler(FrameworkName, handler)
}

func NewPodHandler(baseHandler *framework.BaseHandler, generator framework.TaskGenerator) *PodHandler {
	return &PodHandler{
		BaseHandler: baseHandler,
		generator:   generator,
	}
}

func (h *PodHandler) Name() string {
	return FrameworkName
}

func (pw *PodHandler) ShouldHandle(obj runtime.Object) bool {
	if !pw.BaseHandler.ShouldHandle(obj) {
		return false
	}

	pod, ok := obj.(*corev1.Pod)
	if !ok {
		return false
	}

	// must have annottaion kueue.x-k8s.io/pod-suspending-parent
	if annotations := pod.GetAnnotations(); annotations != nil {
		if _, exists := annotations[consts.AnnotationKueuePodSuspendingParentKey]; exists {
			// if strings.ToLower(parent) == "deployment" || strings.ToLower(parent) == "statefulset" {
			return false // not handl deployment or statefulset managed pod
			// }
		}
	}

	labels := pod.GetLabels()
	if labels == nil {
		return false
	}

	// must have kueue.x-k8s.io/queue-name label
	queueName, hasQueue := labels[consts.LabelKueueQueueNameKey]
	if !hasQueue || queueName == "" {
		return false
	}
	ownerRefs := pod.GetOwnerReferences()
	if len(ownerRefs) > 0 {
		return false
	}

	return true
}

func (h *PodHandler) HandleResourceCreate(obj runtime.Object) {
	task, err := h.Handle(obj)
	if err != nil {
		zlog.SugarErrorf("Failed to handle task creation: %v", err)
	} else {
		zlog.SugarDebugf("Succeeded to handle task creation, task id: %s, task_name: %s", task.ID, task.Name)
	}
}

func (h *PodHandler) Handle(obj runtime.Object) (*model.Task, error) {
	pod, ok := obj.(*corev1.Pod)
	if !ok {
		return nil, fmt.Errorf("object is not a Pod")
	}

	task, err := h.generator.GenerateTask(pod)
	if err != nil {
		return nil, fmt.Errorf("failed to gen task from pod: %w", err)
	}

	err = h.generator.SaveTask(task)
	if err != nil {
		return nil, fmt.Errorf("failed to save task: %w", err)
	}

	return task, nil
}
