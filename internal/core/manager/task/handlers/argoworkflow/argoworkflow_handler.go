package argoworkflow

import (
	"fmt"

	argoworkflowv1alpha1 "github.com/argoproj/argo-workflows/v3/pkg/apis/workflow/v1alpha1"
	"k8s.io/apimachinery/pkg/runtime"
	framework "transwarp.io/mlops/pipeline/internal/core/manager/task/handlerframework"
	model "transwarp.io/mlops/pipeline/internal/core/model/task"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

var (
	gvk = argoworkflowv1alpha1.SchemeGroupVersion.WithKind("Workflow")
)

const (
	FrameworkName = "argoWorkflow"
)

type ArgoWorkflowHandler struct {
	*framework.BaseHandler
	generator framework.TaskGenerator
}

func init() {
	handler := NewArgoWorkflowHandler(
		framework.NewBaseHandler(
			client.MustGetArgoWorkflowClient().WorkflowInformer,
		),

		framework.NewDefaultTaskGenerator(),
	)

	framework.RegisterHandler(FrameworkName, handler)
}

func NewArgoWorkflowHandler(baseHandler *framework.BaseHandler, generator framework.TaskGenerator) *ArgoWorkflowHandler {
	return &ArgoWorkflowHandler{
		BaseHandler: baseHandler,
		generator:   generator,
	}
}

func (h *ArgoWorkflowHandler) Name() string {
	return FrameworkName
}

func (h *ArgoWorkflowHandler) HandleResourceCreate(obj runtime.Object) {
	task, err := h.Handle(obj)
	if err != nil {
		zlog.SugarErrorf("Failed to handle task creation: %v", err)
	} else {
		zlog.SugarDebugf("Succeeded to handle task creation, task id: %s, task_name: %s", task.ID, task.Name)
	}
}

func (h *ArgoWorkflowHandler) Handle(obj runtime.Object) (*model.Task, error) {
	workflow, ok := obj.(*argoworkflowv1alpha1.Workflow)
	if !ok {
		return nil, fmt.Errorf("object is not a Argo Workflow")
	}

	task, err := h.generator.GenerateTask(workflow)
	if err != nil {
		return nil, fmt.Errorf("failed to extract Argo Workflow task info: %w", err)
	}

	err = h.generator.SaveTask(task)
	if err != nil {
		return nil, fmt.Errorf("failed to save task: %w", err)
	}

	return task, nil
}
