package handlers

import (
	_ "transwarp.io/mlops/pipeline/internal/core/manager/task/handlers/appwrapper"
	_ "transwarp.io/mlops/pipeline/internal/core/manager/task/handlers/argoworkflow"
	_ "transwarp.io/mlops/pipeline/internal/core/manager/task/handlers/deployment"
	_ "transwarp.io/mlops/pipeline/internal/core/manager/task/handlers/job"
	_ "transwarp.io/mlops/pipeline/internal/core/manager/task/handlers/jobset"
	_ "transwarp.io/mlops/pipeline/internal/core/manager/task/handlers/leaderworkerset"
	_ "transwarp.io/mlops/pipeline/internal/core/manager/task/handlers/pod"
	_ "transwarp.io/mlops/pipeline/internal/core/manager/task/handlers/raycluster"
	_ "transwarp.io/mlops/pipeline/internal/core/manager/task/handlers/rayjob"
	_ "transwarp.io/mlops/pipeline/internal/core/manager/task/handlers/rayservice"
	_ "transwarp.io/mlops/pipeline/internal/core/manager/task/handlers/statefulset"
)
