package handlerframework

import (
	"context"
	"fmt"
	"os"
	"sync"
	"time"

	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
	"transwarp.io/mlops/pipeline/internal/util"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

var (
	handlerManager HandlerManager
	once           sync.Once

	DefaultLeaderElectionID            = "9f84bee8.tasks.pipeline.llmops.transwarp.io"
	DefaultLeaderElectionLeaseDuration = 15 * time.Second
	DefaultLeaderElectionRenewDeadline = 10 * time.Second
	DefaultLeaderElectionRetryPeriod   = 2 * time.Second
)

func StartHandlerManagerLeaderElection(ctx context.Context) error {
	onStartedLeading := func(ctx context.Context) {
		MustGetHandlerManager().startAll(ctx)
	}
	onStoppedLeading := func() {
		MustGetHandlerManager().stopAll()
	}
	onNewLeader := func(identity string) {
	}
	leaderElectionID := os.Getenv("Leader_Election_ID")
	if leaderElectionID == "" {
		leaderElectionID = DefaultLeaderElectionID
	}
	leConfig := util.NewDefaultLeaderElectionConfig(
		leaderElectionID,
		util.GetCurrentNamespace(),
		util.GetCurrentPodName(),
		client.MustGetK8sClient().Clientset,
		onStartedLeading,
		onStoppedLeading,
		onNewLeader,
		DefaultLeaderElectionLeaseDuration,
		DefaultLeaderElectionRenewDeadline,
		DefaultLeaderElectionRetryPeriod,
	)

	if err := util.StartLeaderElection(ctx, leConfig); err != nil {
		return fmt.Errorf("failed to start task handler leader election: %w", err)
	}

	return nil
}

func MustGetHandlerManager() HandlerManager {
	once.Do(func() {
		handlerManager = NewDefaultHandlerManager()
	})
	if handlerManager == nil {
		panic("handler manager not initialized")
	}
	return handlerManager
}

func RegisterHandler(name string, handler Handler) error {
	return MustGetHandlerManager().registerHandler(name, handler)
}

type HandlerManager interface {
	registerHandler(name string, handler Handler) error
	startAll(ctx context.Context) error
	stopAll() error
}

type DefaultHandlerManager struct {
	handlers map[string]Handler
	mu       sync.RWMutex
}

func NewDefaultHandlerManager() *DefaultHandlerManager {
	return &DefaultHandlerManager{
		handlers: make(map[string]Handler),
	}
}

func (dwm *DefaultHandlerManager) registerHandler(name string, handler Handler) error {
	dwm.mu.Lock()
	defer dwm.mu.Unlock()

	if _, exists := dwm.handlers[name]; exists {
		return fmt.Errorf("handler for %s already registered", name)
	}

	dwm.handlers[name] = handler
	zlog.SugarInfof("Registered %s handler", name)
	return nil
}

func (dwm *DefaultHandlerManager) startAll(ctx context.Context) error {
	dwm.mu.RLock()
	defer dwm.mu.RUnlock()

	for key, handler := range dwm.handlers {
		if !client.MustGetKueueClient().IsKueueSupported() && handler.Name() != "workflow" {
			continue
		}
		zlog.SugarDebugf("start handler: %s %v", key, handler)
		if err := handler.Start(ctx, handler); err != nil {
			zlog.SugarErrorf("Failed to start handler: %s, error: %v", key, err)
			return fmt.Errorf("failed to start handler %s: %w", key, err)
		}
	}

	zlog.SugarInfof("All handlers started successfully")
	return nil
}

func (dwm *DefaultHandlerManager) stopAll() error {
	dwm.mu.RLock()
	defer dwm.mu.RUnlock()

	for key, handler := range dwm.handlers {
		if err := handler.Stop(); err != nil {
			zlog.SugarErrorf("Failed to stop handler: %s, error: %v", key, err)
		}
	}

	zlog.SugarInfoln("All handlers stopped")
	return nil
}
