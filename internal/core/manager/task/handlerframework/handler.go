package handlerframework

import (
	"context"
	"transwarp.io/mlops/mlops-std/stderr"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	kubernetesclientschema "k8s.io/client-go/kubernetes/scheme"
	"k8s.io/client-go/tools/cache"

	"transwarp.io/mlops/pipeline/internal/core/consts"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
	"transwarp.io/mlops/pipeline/internal/util"

	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

type ResourceType struct {
	Group    string
	Version  string
	Resource string
	Kind     string
}

type Handler interface {
	Name() string
	ShouldHandle(obj runtime.Object) bool
	HandleResourceCreate(obj runtime.Object)
	HandleResourceUpdate(oldObj, newObj runtime.Object)
	HandleResourceDelete(obj runtime.Object)
	Start(ctx context.Context, handler <PERSON><PERSON>) error
	Stop() error
}

type BaseHandler struct {
	informer cache.SharedIndexInformer
	stopCh   chan struct{}
}

func NewBaseHandler(informer cache.SharedIndexInformer) *BaseHandler {
	return &BaseHandler{
		informer: informer,
		stopCh:   make(chan struct{}),
	}
}

func (h *BaseHandler) ShouldHandle(obj runtime.Object) bool {
	if !h.hasRequiredLabels(obj) {
		return false
	}

	if !h.isLLMOpsManagedNamespace(obj) {
		return false
	}

	return true
}

func (h *BaseHandler) hasRequiredLabels(obj runtime.Object) bool {
	metaObj, ok := obj.(metav1.Object)
	if !ok {
		return false
	}

	labels := metaObj.GetLabels()
	if labels == nil {
		return false
	}

	// TODO maybe use label selector to filter
	// only deal with task and infer queue
	queueName, hasQueue := labels[consts.LabelKueueQueueNameKey]
	if !hasQueue || queueName != consts.TaskQueueName {
		return false
	}

	return true
}

func (h *BaseHandler) isLLMOpsManagedNamespace(obj runtime.Object) bool {
	metaObj, ok := obj.(metav1.Object)
	if !ok {
		return false
	}

	currentNamespace := util.GetCurrentNamespace()

	namespace := metaObj.GetNamespace()
	nsObj, err := client.MustGetK8sClient().GetNamespace(context.Background(), namespace)
	if err != nil || nsObj == nil {
		return false
	}
	if ns, ok := nsObj.Labels[consts.LabelLLMOpsManagedByKey]; ok && currentNamespace == ns {
		return true
	}
	return false
}

func (h *BaseHandler) Start(ctx context.Context, handler Handler) error {
	if h.informer == nil {
		return stderr.Internal.Errorf("failed to start %s handler, because informer is nil", handler.Name())
	}
	h.informer.AddEventHandler(cache.FilteringResourceEventHandler{
		FilterFunc: func(obj interface{}) bool {
			return handler.ShouldHandle(obj.(runtime.Object))
		},
		Handler: cache.ResourceEventHandlerFuncs{
			AddFunc: func(obj interface{}) {
				if runtimeObj, ok := obj.(runtime.Object); ok {
					gvks, _, err := kubernetesclientschema.Scheme.ObjectKinds(runtimeObj)
					if err == nil && len(gvks) > 0 {
						runtimeObj.GetObjectKind().SetGroupVersionKind(gvks[0])
					} else {
						zlog.SugarErrorf("Failed to get object kinds: %v", err)
					}
					handler.HandleResourceCreate(runtimeObj)
				}
			},
			UpdateFunc: func(oldObj, newObj interface{}) {
				if oldRuntimeObj, ok := oldObj.(runtime.Object); ok {
					if newRuntimeObj, ok := newObj.(runtime.Object); ok {
						handler.HandleResourceUpdate(oldRuntimeObj, newRuntimeObj)
					}
				}
			},
			DeleteFunc: func(obj interface{}) {
				if runtimeObj, ok := obj.(runtime.Object); ok {
					handler.HandleResourceDelete(runtimeObj)
				}
			},
		},
	})

	go h.informer.Run(h.stopCh)

	if !cache.WaitForCacheSync(ctx.Done(), h.informer.HasSynced) {
		zlog.SugarWarnf("Cache for %s synced failed.", handler.Name())
	}

	zlog.SugarInfof("Started %s handler", handler.Name())
	return nil
}

func (h *BaseHandler) Stop() error {
	close(h.stopCh)
	return nil
}

func (h *BaseHandler) HandleResourceCreate(obj runtime.Object) {
	zlog.SugarDebugf("Resource created", "object", obj)
}

func (h *BaseHandler) HandleResourceUpdate(oldObj, newObj runtime.Object) {
	zlog.SugarDebugf("Resource updated", "object", newObj)
}

func (h *BaseHandler) HandleResourceDelete(obj runtime.Object) {
	zlog.SugarDebugf("Resource deleted", "object", obj)
}
