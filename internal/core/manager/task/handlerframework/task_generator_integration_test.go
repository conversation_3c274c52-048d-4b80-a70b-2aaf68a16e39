package handlerframework

import (
	"testing"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"

	"transwarp.io/mlops/pipeline/internal/core/consts"
	modeltask "transwarp.io/mlops/pipeline/internal/core/model/task"
)

func TestDefaultTaskGenerator_GenerateTaskWithYamlManifest(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	generator := NewDefaultTaskGenerator()

	// Create a test pod
	pod := &corev1.Pod{
		TypeMeta: metav1.TypeMeta{
			APIVersion: "v1",
			Kind:       "Pod",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-pod",
			Namespace: "default",
			UID:       "test-uid",
			CreationTimestamp: metav1.Time{
				Time: time.Now(),
			},
			Annotations: map[string]string{
				consts.AnnotationTaskNameKey:        "Test Task",
				consts.AnnotationTaskDescriptionKey: "Test Description",
				consts.AnnotationTaskCreatorKey:     "test-user",
			},
			Labels: map[string]string{
				consts.LabelKueueQueueNameKey: "default-queue",
			},
		},
		Spec: corev1.PodSpec{
			Containers: []corev1.Container{
				{
					Name:  "test-container",
					Image: "nginx:latest",
				},
			},
		},
	}

	// Set GVK for the pod
	pod.SetGroupVersionKind(schema.GroupVersionKind{
		Group:   "",
		Version: "v1",
		Kind:    "Pod",
	})

	// Generate task
	task, err := generator.GenerateTask(pod)
	if err != nil {
		t.Fatalf("GenerateTask failed: %v", err)
	}

	// Verify task properties
	if task == nil {
		t.Fatal("Task should not be nil")
	}

	if task.ID == "" {
		t.Error("Task ID should not be empty")
	}

	if task.YamlRawManifest == "" {
		t.Error("YamlRawManifest should not be empty")
	}

	// Verify that task ID was added to pod annotations
	if pod.Annotations[consts.AnnotationTaskIDKey] != task.ID {
		t.Errorf("Expected task ID %s in pod annotations, got %s",
			task.ID, pod.Annotations[consts.AnnotationTaskIDKey])
	}

	// Verify YAML contains the task ID annotation
	if !containsString(task.YamlRawManifest, task.ID) {
		t.Error("YamlRawManifest should contain the task ID")
	}

	// Verify YAML contains expected content
	if !containsString(task.YamlRawManifest, "test-pod") {
		t.Error("YamlRawManifest should contain pod name")
	}

	if !containsString(task.YamlRawManifest, "nginx:latest") {
		t.Error("YamlRawManifest should contain container image")
	}

	t.Logf("Generated task ID: %s", task.ID)
	t.Logf("YAML manifest length: %d bytes", len(task.YamlRawManifest))
}

func TestDefaultTaskGenerator_SaveTaskWithYamlManifest(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	generator := NewDefaultTaskGenerator()

	// Create a simple task
	task := &modeltask.Task{
		ID:              "test-task-id",
		Name:            "Test Task",
		Type:            modeltask.TaskTypeUnknown,
		Priority:        modeltask.TaskPriorityMedium,
		Status:          modeltask.TaskStatusPending,
		YamlRawManifest: "apiVersion: v1\nkind: Pod\nmetadata:\n  name: test-pod",
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	// Save task
	err := generator.SaveTask(task)
	if err != nil {
		t.Fatalf("SaveTask failed: %v", err)
	}

	t.Logf("Successfully saved task with ID: %s", task.ID)
}

// Helper function to check if a string contains a substring
func containsString(s, substr string) bool {
	return len(s) >= len(substr) &&
		(s == substr || len(s) > len(substr) &&
			(s[:len(substr)] == substr || s[len(s)-len(substr):] == substr ||
				containsSubstring(s, substr)))
}

func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
