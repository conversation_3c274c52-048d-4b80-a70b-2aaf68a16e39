package common

import (
	"time"

	"gopkg.in/yaml.v3"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	kueuev1beta1 "sigs.k8s.io/kueue/apis/kueue/v1beta1"

	"transwarp.io/mlops/pipeline/internal/core/consts"
	modeltask "transwarp.io/mlops/pipeline/internal/core/model/task"
	daomodel "transwarp.io/mlops/pipeline/internal/dao/model"

	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

type TaskBuilder struct {
	taskIDGen *TaskIDGenerator
}

func NewTaskBuilder() *TaskBuilder {
	return &TaskBuilder{
		taskIDGen: NewTaskIDGenerator(),
	}
}

type TaskBuildOptions struct {
	ID        string
	Status    modeltask.TaskStatus
	QueueName string

	SubTasks  []*modeltask.SubTask
	Resources *modeltask.Resources
	NodeNames []string
	PodCount  int

	Workload *kueuev1beta1.Workload

	GVK schema.GroupVersionKind
	GVR schema.GroupVersionResource
}

func (tb *TaskBuilder) BuildTaskFromObject(obj metav1.Object, opts *TaskBuildOptions) *modeltask.Task {
	if obj == nil {
		return nil
	}

	// Generate ID if not provided
	id := opts.ID
	if id == "" {
		if runtimeObj, ok := obj.(runtime.Object); ok {
			id = tb.taskIDGen.GenerateTaskID(runtimeObj)
		} else {
			// For metav1.Object that doesn't implement runtime.Object,
			// we need to generate ID manually using namespace/name/uid
			source := obj.GetNamespace() + "/" + obj.GetName() + "/" + string(obj.GetUID())
			id = "task-" + source // Simplified ID generation
		}
	}

	annotations := obj.GetAnnotations()
	if annotations == nil {
		annotations = make(map[string]string)
	}

	labels := obj.GetLabels()
	if labels == nil {
		labels = make(map[string]string)
	}

	task := &modeltask.Task{
		ID:   id,
		Name: tb.extractName(obj),
		Type: tb.extractType(annotations),
		// set default, when workload detect will update to workload priority
		Priority:    modeltask.TaskPriorityDefault,
		Description: annotations[consts.AnnotationTaskDescriptionKey],
		Creator:     annotations[consts.AnnotationTaskCreatorKey],
		CreatedAt:   tb.extractCreatedAt(annotations, obj),
		UpdatedAt:   obj.GetCreationTimestamp().Time,
		Status:      tb.extractStatus(opts),
		StartedAt:   nil,
		EndedAt:     nil,

		TenantID:    annotations[consts.AnnotationTaskTenantIDKey],
		TenantName:  annotations[consts.AnnotationTaskTenantNameKey],
		ProjectID:   annotations[consts.AnnotationTaskProjectIDKey],
		ProjectName: annotations[consts.AnnotationTaskProjectNameKey],

		RefSourceID: tb.extractRefSourceID(obj),
		RefRunID:    tb.extractRefRunID(obj),

		QueueName: tb.extractQueueName(labels, opts),

		Resources: opts.Resources,
		NodeNames: opts.NodeNames,
		SubTasks:  opts.SubTasks,
		PodCount:  opts.PodCount,

		Extra: make(map[string]string),

		Namespace:   obj.GetNamespace(),
		Labels:      labels,
		Annotations: annotations,
		GVK:         opts.GVK,
		GVR:         opts.GVR,
	}

	if opts.Workload != nil {
		tb.enrichTaskWithWorkload(task, opts.Workload)
	}

	// Add task ID to annotations before converting to YAML
	annotations[consts.AnnotationTaskIDKey] = task.ID
	annotations[consts.AnnotationTaskCreateAtKey] = task.CreatedAt.Format(time.RFC3339)
	annotations[consts.AnnotationTaskRefRunIDKey] = task.RefRunID
	obj.SetAnnotations(annotations)

	// yaml, err := ToObjYaml(obj.GetNamespace(), obj.GetName(), obj.(runtime.Object).GetObjectKind().GroupVersionKind())
	yamlStr, err := yaml.Marshal(obj)
	if err != nil {
		zlog.SugarErrorf("Failed to marshal object to YAML: %v", err)
	} else {
		task.YamlRawManifest = string(yamlStr)
	}

	return task
}

func (tb *TaskBuilder) BuildTaskFromWorkloadAndRef(workload *kueuev1beta1.Workload, ref metav1.Object, opts *TaskBuildOptions) *modeltask.Task {
	if workload == nil || ref == nil {
		return nil
	}

	if opts == nil {
		opts = &TaskBuildOptions{}
	}
	opts.Workload = workload

	task := tb.BuildTaskFromObject(ref, opts)
	if task == nil {
		return nil
	}

	tb.enrichTaskWithWorkload(task, workload)

	return task
}

func (tb *TaskBuilder) extractName(obj metav1.Object) string {
	if annotations := obj.GetAnnotations(); annotations != nil {
		if name, exists := annotations[consts.AnnotationTaskNameKey]; exists && name != "" {
			return name
		}
	}

	return obj.GetName()
}

func (tb *TaskBuilder) extractType(annotations map[string]string) modeltask.TaskType {
	if taskType := annotations[consts.AnnotationTaskTypeKey]; taskType != "" {
		return modeltask.ToTaskType(taskType)
	}

	return modeltask.TaskTypeUnknown
}

func (tb *TaskBuilder) extractCreatedAt(annotations map[string]string, obj metav1.Object) time.Time {
	if annotations != nil {
		if createdAt, exists := annotations[consts.AnnotationTaskCreateAtKey]; exists && createdAt != "" {
			t, err := time.Parse(time.RFC3339, createdAt)
			if err != nil {
				zlog.Warnf("Failed to parse created_at: %v", err)
				return obj.GetCreationTimestamp().Time
			}
			return t
		}
	}

	return obj.GetCreationTimestamp().Time
}

func (tb *TaskBuilder) extractStatus(opts *TaskBuildOptions) modeltask.TaskStatus {
	if opts != nil && opts.Status != "" {
		return opts.Status
	}
	return modeltask.TaskStatusPending
}

func (tb *TaskBuilder) extractQueueName(labels map[string]string, opts *TaskBuildOptions) string {
	if opts != nil && opts.QueueName != "" {
		return opts.QueueName
	}
	return labels[consts.LabelKueueQueueNameKey]
}

func (tb *TaskBuilder) extractRefSourceID(obj metav1.Object) string {
	if annotations := obj.GetAnnotations(); annotations != nil {
		if sourceID, exists := annotations[consts.AnnotationTaskRefSourceIDKey]; exists {
			return sourceID
		}
	}

	if labels := obj.GetLabels(); labels != nil {
		if sourceID, exists := labels[consts.LabelTaskRefSourceIDKey]; exists {
			return sourceID
		}
	}

	return ""
}
func (tb *TaskBuilder) extractRefRunID(obj metav1.Object) string {
	if annotations := obj.GetAnnotations(); annotations != nil {
		if runID, exists := annotations[consts.AnnotationTaskRefRunIDKey]; exists {
			return runID
		}
	}
	if labels := obj.GetLabels(); labels != nil {
		if runID, exists := labels[consts.LabelTaskRefRunIDKey]; exists {
			return runID
		}
	}

	return string(obj.GetUID())
}

func (tb *TaskBuilder) enrichTaskWithWorkload(task *modeltask.Task, workload *kueuev1beta1.Workload) {
	if task == nil || workload == nil {
		return
	}

	if workload.Spec.Priority != nil {
		task.Priority = modeltask.ToTaskPriority(workload.Spec.Priority)
	}

	if workload.Spec.QueueName != "" {
		task.QueueName = string(workload.Spec.QueueName)
	}

	if workload.Status.Admission != nil {
		task.ClusterQueueName = string(workload.Status.Admission.ClusterQueue)
	}

	task.UpdateTaskStatus().
		UpdateTaskStartedAt().
		UpdateTaskEndedAt().
		UpdateQueueDuration()
}

func (tb *TaskBuilder) UpdateTaskWithRuntimeInfo(task *modeltask.Task, runtimeInfo *TaskRuntimeInfo) {
	if task == nil || runtimeInfo == nil {
		return
	}

	task.Resources = runtimeInfo.Resources
	task.NodeNames = runtimeInfo.NodeNames
	task.SubTasks = runtimeInfo.SubTasks
	task.PodCount = runtimeInfo.PodCount
}

type TaskRuntimeInfo struct {
	Resources *modeltask.Resources
	NodeNames []string
	SubTasks  []*modeltask.SubTask
	PodCount  int
}

func (tb *TaskBuilder) ToDAOModel(task *modeltask.Task) (*daomodel.Task, error) {
	return ToDAOModel(task)
}

func (tb *TaskBuilder) FromDAOModel(daoTask *daomodel.Task) (*modeltask.Task, error) {
	return FromDAOModel(daoTask)
}
