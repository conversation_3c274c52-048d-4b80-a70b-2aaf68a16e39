package common

import (
	"encoding/json"

	"k8s.io/apimachinery/pkg/runtime/schema"
	modeltask "transwarp.io/mlops/pipeline/internal/core/model/task"
	daomodel "transwarp.io/mlops/pipeline/internal/dao/model"
)

// ToDAOModel converts a core task model to DAO model for database storage
func ToDAOModel(task *modeltask.Task) (*daomodel.Task, error) {
	if task == nil {
		return nil, nil
	}

	daoTask := &daomodel.Task{
		ID:               task.ID,
		Name:             task.Name,
		Type:             string(task.Type),
		Priority:         string(task.Priority),
		Description:      task.Description,
		Creator:          task.Creator,
		CreatedAt:        task.CreatedAt,
		UpdatedAt:        task.UpdatedAt,
		Status:           string(task.Status),
		TenantID:         task.TenantID,
		TenantName:       task.TenantName,
		ProjectID:        task.ProjectID,
		ProjectName:      task.ProjectName,
		YamlRawManifest:  task.YamlRawManifest,
		QueueName:        task.QueueName,
		ClusterQueueName: task.ClusterQueueName,
		Namespace:        task.Namespace,

		RefSourceID: task.RefSourceID,
		RefRunID:    task.RefRunID,
	}

	// Set optional time fields
	if task.StartedAt != nil {
		daoTask.StartedAt = task.StartedAt
	}
	if task.EndedAt != nil {
		daoTask.EndedAt = task.EndedAt
	}

	if task.Extra != nil {
		if extraJSON, err := json.Marshal(task.Extra); err == nil {
			daoTask.Extra = string(extraJSON)
		}
	}

	if task.Resources != nil {
		// deep copy resource and set used to 0
		resources := &modeltask.Resources{
			Requests: task.Resources.Requests,
			Limits:   task.Resources.Limits,
			Used:     modeltask.NewResourceSpec(),
		}
		if resourcesJSON, err := json.Marshal(resources); err == nil {
			daoTask.Resources = string(resourcesJSON)
		}
	}

	if task.Labels != nil {
		if labelsJSON, err := json.Marshal(task.Labels); err == nil {
			daoTask.Labels = string(labelsJSON)
		}
	}

	if task.Annotations != nil {
		if annotationsJSON, err := json.Marshal(task.Annotations); err == nil {
			daoTask.Annotations = string(annotationsJSON)
		}
	}

	if gvkJSON, err := json.Marshal(task.GVK); err == nil {
		daoTask.Gvk = string(gvkJSON)
	}

	if gvrJSON, err := json.Marshal(task.GVR); err == nil {
		daoTask.Gvr = string(gvrJSON)
	}

	return daoTask, nil
}

// FromDAOModel converts a DAO model to core task model
func FromDAOModel(daoTask *daomodel.Task) (*modeltask.Task, error) {
	if daoTask == nil {
		return nil, nil
	}

	task := &modeltask.Task{
		ID:               daoTask.ID,
		Name:             daoTask.Name,
		Type:             modeltask.ToTaskType(daoTask.Type),
		Priority:         modeltask.TaskPriority(daoTask.Priority),
		Description:      daoTask.Description,
		Creator:          daoTask.Creator,
		CreatedAt:        daoTask.CreatedAt,
		UpdatedAt:        daoTask.UpdatedAt,
		Status:           modeltask.TaskStatus(daoTask.Status),
		TenantID:         daoTask.TenantID,
		TenantName:       daoTask.TenantName,
		ProjectID:        daoTask.ProjectID,
		ProjectName:      daoTask.ProjectName,
		YamlRawManifest:  daoTask.YamlRawManifest,
		QueueName:        daoTask.QueueName,
		ClusterQueueName: daoTask.ClusterQueueName,
		Namespace:        daoTask.Namespace,

		RefSourceID: daoTask.RefSourceID,
		RefRunID:    daoTask.RefRunID,
	}

	// Set optional time fields
	if nil != daoTask.StartedAt && !daoTask.StartedAt.IsZero() {
		task.StartedAt = daoTask.StartedAt
	}
	if nil != daoTask.EndedAt && !daoTask.EndedAt.IsZero() {
		task.EndedAt = daoTask.EndedAt
	}

	if daoTask.Extra != "" {
		var extra map[string]string
		if err := json.Unmarshal([]byte(daoTask.Extra), &extra); err == nil {
			task.Extra = extra
		}
	}

	if daoTask.Resources != "" {
		var resources modeltask.Resources
		if err := json.Unmarshal([]byte(daoTask.Resources), &resources); err == nil {
			task.Resources = &resources
		}
	}

	if daoTask.Labels != "" {
		var labels map[string]string
		if err := json.Unmarshal([]byte(daoTask.Labels), &labels); err == nil {
			task.Labels = labels
		}
	}

	if daoTask.Annotations != "" {
		var annotations map[string]string
		if err := json.Unmarshal([]byte(daoTask.Annotations), &annotations); err == nil {
			task.Annotations = annotations
		}
	}

	if daoTask.Gvk != "" {
		var gvk schema.GroupVersionKind
		if err := json.Unmarshal([]byte(daoTask.Gvk), &gvk); err == nil {
			task.GVK = gvk
		}
	}

	if daoTask.Gvr != "" {
		var gvr schema.GroupVersionResource
		if err := json.Unmarshal([]byte(daoTask.Gvr), &gvr); err == nil {
			task.GVR = gvr
		}
	}

	// cal QueuedDuration
	task.UpdateQueueDuration()

	return task, nil
}
