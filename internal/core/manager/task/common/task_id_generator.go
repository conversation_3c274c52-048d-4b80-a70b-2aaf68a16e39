package common

import (
	"crypto/md5"
	"fmt"
	"strings"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"

	"transwarp.io/mlops/pipeline/internal/core/consts"
)

// TaskIDGenerator provides unified TaskID generation logic for both handlers and cache
type TaskIDGenerator struct{}

// NewTaskIDGenerator creates a new TaskIDGenerator instance
func NewTaskIDGenerator() *TaskIDGenerator {
	return &TaskIDGenerator{}
}

// GenerateTaskID generates a consistent TaskID for k8s resources
// For pod group tasks (multiple pods with same pod-group-name), it uses the pod-group-name
// For other resources, it uses the resource's namespace/name/uid
func (g *TaskIDGenerator) GenerateTaskID(obj runtime.Object) string {
	metaObj, ok := obj.(metav1.Object)
	if !ok {
		return ""
	}

	gvk := obj.GetObjectKind().GroupVersionKind()
	// For pod group tasks, use pod-group-name to ensure consistent TaskID
	if gvk.Kind == "Pod" {
		if labels := metaObj.GetLabels(); labels != nil {
			if g.isPlainPodsTask(metaObj) {
				// Use pod-group-name + namespace to generate consistent TaskID for all pods in the group
				podGroupName := labels[consts.LabelKueuePodGroupNameKey]
				if podGroupName == "" {
					return g.safeName(fmt.Sprintf("%s-%s", strings.ToLower(gvk.Kind), metaObj.GetUID()))
				} else {
					source := fmt.Sprintf("%s/%s", metaObj.GetNamespace(), podGroupName)
					hash := md5.Sum([]byte(source))
					hashStr := fmt.Sprintf("%x", hash)
					return g.safeName(fmt.Sprintf("%s-%s", strings.ToLower(gvk.Kind), hashStr))
				}
			}
		}
	}

	// if task id exists in annotation, this is rerun task, do not generate new task id
	annotations := metaObj.GetAnnotations()
	if annotations != nil {
		if taskID, exists := annotations[consts.AnnotationTaskIDKey]; exists && taskID != "" {
			return taskID
		}
	}

	return g.safeName(fmt.Sprintf("%s-%s", strings.ToLower(gvk.Kind), metaObj.GetUID()))
}

func (g *TaskIDGenerator) safeName(str string) string {
	if len(str) > 63 {
		return str[:63]
	}
	return str
}

func (g *TaskIDGenerator) isPlainPodsTask(metaObj metav1.Object) bool {
	labels := metaObj.GetLabels()
	if labels == nil {
		return false
	}

	// Check for queue-name label
	queueName, hasQueue := labels[consts.LabelKueueQueueNameKey]
	if !hasQueue || queueName == "" {
		return false
	}

	// Check that it's not a pod with suspending parent (not a plain pod group)
	if annotations := metaObj.GetAnnotations(); annotations != nil {
		if _, exists := annotations[consts.AnnotationKueuePodSuspendingParentKey]; exists {
			return false
		}
	}

	if ownerRefs := metaObj.GetOwnerReferences(); len(ownerRefs) > 0 {
		return false
	}

	return true
}
