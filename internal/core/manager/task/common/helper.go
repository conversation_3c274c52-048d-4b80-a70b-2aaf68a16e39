package common

import (
	"context"
	"encoding/json"
	"fmt"
	"reflect"

	eventsv1alpha1 "github.com/argoproj/argo-events/pkg/apis/events/v1alpha1"
	wfv1 "github.com/argoproj/argo-workflows/v3/pkg/apis/workflow/v1alpha1"
	appwrapperv1beta2 "github.com/project-codeflare/appwrapper/api/v1beta2"
	rayv1 "github.com/ray-project/kuberay/ray-operator/apis/ray/v1alpha1"
	appsv1 "k8s.io/api/apps/v1"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	jobsetv1alpha2 "sigs.k8s.io/jobset/api/jobset/v1alpha2"
	lwsapiv1 "sigs.k8s.io/lws/api/leaderworkerset/v1"

	"gopkg.in/yaml.v3"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	kueuev1beta1 "sigs.k8s.io/kueue/apis/kueue/v1beta1"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
	"transwarp.io/mlops/pipeline/internal/util"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

func GetObjectFromOwnerRef(namespace string, ownerRef metav1.OwnerReference) (metav1.Object, error) {
	name := ownerRef.Name
	kind := ownerRef.Kind

	gvk, err := InferGVKFromOwnerRef(ownerRef)
	if err != nil {
		zlog.SugarErrorf("Failed to infer GVk from owner reference: %v", err)
		return nil, err
	}

	obj, err := GetObj(namespace, name, gvk)
	if err != nil {
		return nil, fmt.Errorf("failed to get %s %s/%s: %w", kind, namespace, name, err)
	}

	return obj, nil
}

func FindAllAncestor(obj metav1.Object) ([]metav1.Object, error) {
	ancestors := make([]metav1.Object, 0)
	namespace := obj.GetNamespace()

	for _, ref := range obj.GetOwnerReferences() {
		ancestor, err := FindAncestorFromOwnerRef(namespace, ref)
		if err != nil {
			return nil, err
		}
		ancestors = append(ancestors, ancestor)
	}

	return ancestors, nil
}

func FindAncestorFromOwnerRef(namespace string, ownerRef metav1.OwnerReference) (metav1.Object, error) {
	var ancestor metav1.Object
	for {
		name := ownerRef.Name
		kind := ownerRef.Kind

		gvk, err := InferGVKFromOwnerRef(ownerRef)
		if err != nil {
			zlog.SugarErrorf("Failed to infer GVK from owner reference: %v", err)
			return nil, err
		}

		obj, err := GetObj(namespace, name, gvk)
		if err != nil {
			return nil, fmt.Errorf("failed to get %s %s/%s: %w", kind, namespace, name, err)
		}
		if obj == nil {
			return nil, fmt.Errorf("failed to get %s %s/%s: not found", kind, namespace, name)
		}
		ancestor = obj

		if len(obj.GetOwnerReferences()) == 0 {
			break
		}
		ownerRef = obj.GetOwnerReferences()[0]
	}

	return ancestor, nil
}

func FindAnyAncestor(obj metav1.Object) (metav1.Object, error) {
	ancestor := obj
	namespace := obj.GetNamespace()
	for {
		ownerRefs := ancestor.GetOwnerReferences()
		if len(ownerRefs) == 0 {
			break
		}

		ownerRef := ownerRefs[0]
		name := ownerRef.Name
		kind := ownerRef.Kind

		gvk, err := InferGVKFromOwnerRef(ownerRef)
		if err != nil {
			zlog.SugarErrorf("Failed to infer GVK from owner reference: %v", err)
			return nil, err
		}

		obj, err := GetObj(namespace, name, gvk)
		if err != nil {
			return nil, fmt.Errorf("failed to get %s %s/%s: %w", kind, namespace, name, err)
		}
		ancestor = obj
	}

	return ancestor, nil
}

func InferGVRFromOwnerRef(ownerRef metav1.OwnerReference) (schema.GroupVersionResource, error) {
	gv, err := schema.ParseGroupVersion(ownerRef.APIVersion)
	if err != nil {
		return schema.GroupVersionResource{}, fmt.Errorf("failed to parse APIVersion %s: %w", ownerRef.APIVersion, err)
	}

	gvk := gv.WithKind(ownerRef.Kind)
	return util.InferGVRFromGVK(gvk), nil
}

func InferGVKFromOwnerRef(ownerRef metav1.OwnerReference) (schema.GroupVersionKind, error) {
	gv, err := schema.ParseGroupVersion(ownerRef.APIVersion)
	if err != nil {
		return schema.GroupVersionKind{}, fmt.Errorf("failed to parse APIVersion %s: %w", ownerRef.APIVersion, err)
	}

	return gv.WithKind(ownerRef.Kind), nil
}

func InferGVRFromObject(obj metav1.Object) schema.GroupVersionResource {
	gvk := obj.(runtime.Object).GetObjectKind().GroupVersionKind()
	return util.InferGVRFromGVK(gvk)
}

func InferGVKFromObject(obj metav1.Object) schema.GroupVersionKind {
	return obj.(runtime.Object).GetObjectKind().GroupVersionKind()
}

func GetObj(namespace, name string, gvk schema.GroupVersionKind) (metav1.Object, error) {
	switch gvk.Kind {
	case "Pod":
		return client.MustGetK8sClient().GetPod(namespace, name)
	case "Deployment":
		return client.MustGetK8sClient().GetDeployment(namespace, name)
	case "ReplicaSet":
		return client.MustGetK8sClient().GetReplicaSet(namespace, name)
	case "StatefulSet":
		return client.MustGetK8sClient().GetStatefulSet(namespace, name)
	case "DaemonSet":
		return client.MustGetK8sClient().GetDaemonSet(namespace, name)
	case "Job":
		return client.MustGetK8sClient().GetJob(namespace, name)
	case "CronJob":
		return client.MustGetK8sClient().GetCronJob(namespace, name)
	case "Workload":
		return client.MustGetKueueClient().GetWorkload(namespace, name)
	case "JobSet":
		return client.MustGetJobSetClient().GetJobSet(namespace, name)
	case "LeaderWorkerSet":
		return client.MustGetLeaderWorkerSetClient().GetLeaderWorkerSet(namespace, name)
	case "Workflow":
		return client.MustGetArgoWorkflowClient().GetWorkflow(namespace, name)
	case "CronWorkflow":
		return client.MustGetArgoWorkflowClient().GetCronWorkflow(namespace, name)
	case "RayJob":
		return client.MustGetRayClient().GetRayJob(namespace, name)
	case "RayCluster":
		return client.MustGetRayClient().GetRayCluster(namespace, name)
	case "AppWrapper":
		return client.MustGetAppWrapperClient().GetAppWrapper(namespace, name)
	case "EventBus":
		return client.MustGetArgoEventClient().GetEventBus(namespace, name)
	default:
		gvr := util.InferGVRFromGVK(gvk)
		obj, err := client.MustGetK8sClient().Get(context.Background(), namespace, gvr, name)
		if err != nil {
			return nil, fmt.Errorf("failed to get %s %s/%s: %w", gvr.Resource, namespace, name, err)
		}
		return obj, nil
	}
}

func ToObjYaml(namespace, name string, gvk schema.GroupVersionKind) (string, error) {
	switch gvk.Kind {
	case "Pod":
		obj, err := client.MustGetK8sClient().GetPod(namespace, name)
		if err != nil {
			return "", err
		}
		yaml, err := yaml.Marshal(obj)
		if err != nil {
			return "", err
		}
		return string(yaml), nil
	case "Deployment":
		obj, err := client.MustGetK8sClient().GetDeployment(namespace, name)
		if err != nil {
			return "", err
		}
		yaml, err := yaml.Marshal(obj)
		if err != nil {
			return "", err
		}
		return string(yaml), nil
	case "ReplicaSet":
		obj, err := client.MustGetK8sClient().GetReplicaSet(namespace, name)
		if err != nil {
			return "", err
		}
		yaml, err := yaml.Marshal(obj)
		if err != nil {
			return "", err
		}
		return string(yaml), nil
	case "StatefulSet":
		obj, err := client.MustGetK8sClient().GetStatefulSet(namespace, name)
		if err != nil {
			return "", err
		}
		yaml, err := yaml.Marshal(obj)
		if err != nil {
			return "", err
		}
		return string(yaml), nil
	case "DaemonSet":
		obj, err := client.MustGetK8sClient().GetDaemonSet(namespace, name)
		if err != nil {
			return "", err
		}
		yaml, err := yaml.Marshal(obj)
		if err != nil {
			return "", err
		}
		return string(yaml), nil
	case "Job":
		obj, err := client.MustGetK8sClient().GetJob(namespace, name)
		if err != nil {
			return "", err
		}
		yaml, err := yaml.Marshal(obj)
		if err != nil {
			return "", err
		}
		return string(yaml), nil
	case "CronJob":
		obj, err := client.MustGetK8sClient().GetCronJob(namespace, name)
		if err != nil {
			return "", err
		}
		yaml, err := yaml.Marshal(obj)
		if err != nil {
			return "", err
		}
		return string(yaml), nil
	case "Workload":
		obj, err := client.MustGetKueueClient().GetWorkload(namespace, name)
		if err != nil {
			return "", err
		}
		yaml, err := yaml.Marshal(obj)
		if err != nil {
			return "", err
		}
		return string(yaml), nil
	case "JobSet":
		obj, err := client.MustGetJobSetClient().GetJobSet(namespace, name)
		if err != nil {
			return "", err
		}
		yaml, err := yaml.Marshal(obj)
		if err != nil {
			return "", err
		}
		return string(yaml), nil
	case "LeaderWorkerSet":
		obj, err := client.MustGetLeaderWorkerSetClient().GetLeaderWorkerSet(namespace, name)
		if err != nil {
			return "", err
		}
		yaml, err := yaml.Marshal(obj)
		if err != nil {
			return "", err
		}
		return string(yaml), nil
	case "Workflow":
		obj, err := client.MustGetArgoWorkflowClient().GetWorkflow(namespace, name)
		if err != nil {
			return "", err
		}
		yaml, err := yaml.Marshal(obj)
		if err != nil {
			return "", err
		}
		return string(yaml), nil
	case "CronWorkflow":
		obj, err := client.MustGetArgoWorkflowClient().GetCronWorkflow(namespace, name)
		if err != nil {
			return "", err
		}
		yaml, err := yaml.Marshal(obj)
		if err != nil {
			return "", err
		}
		return string(yaml), nil
	case "RayJob":
		obj, err := client.MustGetRayClient().GetRayJob(namespace, name)
		if err != nil {
			return "", err
		}
		yaml, err := yaml.Marshal(obj)
		if err != nil {
			return "", err
		}
		return string(yaml), nil
	case "RayCluster":
		obj, err := client.MustGetRayClient().GetRayCluster(namespace, name)
		if err != nil {
			return "", err
		}
		yaml, err := yaml.Marshal(obj)
		if err != nil {
			return "", err
		}
		return string(yaml), nil
	case "AppWrapper":
		obj, err := client.MustGetAppWrapperClient().GetAppWrapper(namespace, name)
		if err != nil {
			return "", err
		}
		yaml, err := yaml.Marshal(obj)
		if err != nil {
			return "", err
		}
		return string(yaml), nil
	case "EventBus":
		obj, err := client.MustGetArgoEventClient().GetEventBus(namespace, name)
		if err != nil {
			return "", err
		}
		yaml, err := yaml.Marshal(obj)
		if err != nil {
			return "", err
		}
		return string(yaml), nil
	default:
		gvr := util.InferGVRFromGVK(gvk)
		obj, err := client.MustGetK8sClient().Get(context.Background(), namespace, gvr, name)
		if err != nil {
			return "", fmt.Errorf("failed to get %s %s/%s: %w", gvr.Resource, namespace, name, err)
		}
		yaml, err := yaml.Marshal(obj)
		if err != nil {
			return "", err
		}
		return string(yaml), nil
	}
}

func GetObjFromYaml(gvk schema.GroupVersionKind, yamlManifest string) (runtime.Object, error) {
	switch gvk.Kind {
	case "Pod":
		obj := &corev1.Pod{}
		if err := yaml.Unmarshal([]byte(yamlManifest), obj); err != nil {
			return nil, err
		}
		return obj, nil
	case "Deployment":
		obj := &appsv1.Deployment{}
		if err := yaml.Unmarshal([]byte(yamlManifest), obj); err != nil {
			return nil, err
		}
		return obj, nil
	case "ReplicaSet":
		obj := &appsv1.ReplicaSet{}
		if err := yaml.Unmarshal([]byte(yamlManifest), obj); err != nil {
			return nil, err
		}
		return obj, nil
	case "StatefulSet":
		obj := &appsv1.StatefulSet{}
		if err := yaml.Unmarshal([]byte(yamlManifest), obj); err != nil {
			return nil, err
		}
		return obj, nil
	case "DaemonSet":
		obj := &appsv1.DaemonSet{}
		if err := yaml.Unmarshal([]byte(yamlManifest), obj); err != nil {
			return nil, err
		}
		return obj, nil
	case "Job":
		obj := &batchv1.Job{}
		if err := yaml.Unmarshal([]byte(yamlManifest), obj); err != nil {
			return nil, err
		}
		return obj, nil
	case "CronJob":
		obj := &batchv1.CronJob{}
		if err := yaml.Unmarshal([]byte(yamlManifest), obj); err != nil {
			return nil, err
		}
		return obj, nil
	case "Workload":
		obj := &kueuev1beta1.Workload{}
		if err := yaml.Unmarshal([]byte(yamlManifest), obj); err != nil {
			return nil, err
		}
		return obj, nil
	case "JobSet":
		obj := &jobsetv1alpha2.JobSet{}
		if err := yaml.Unmarshal([]byte(yamlManifest), obj); err != nil {
			return nil, err
		}
		return obj, nil
	case "LeaderWorkerSet":
		obj := &lwsapiv1.LeaderWorkerSet{}
		if err := yaml.Unmarshal([]byte(yamlManifest), obj); err != nil {
			return nil, err
		}
		return obj, nil
	case "Workflow":
		obj := &wfv1.Workflow{}
		if err := yaml.Unmarshal([]byte(yamlManifest), obj); err != nil {
			return nil, err
		}
		return obj, nil
	case "CronWorkflow":
		obj := &wfv1.CronWorkflow{}
		if err := yaml.Unmarshal([]byte(yamlManifest), obj); err != nil {
			return nil, err
		}
		return obj, nil
	case "RayJob":
		obj := &rayv1.RayJob{}
		if err := yaml.Unmarshal([]byte(yamlManifest), obj); err != nil {
			return nil, err
		}
		return obj, nil
	case "RayService":
		obj := &rayv1.RayService{}
		if err := yaml.Unmarshal([]byte(yamlManifest), obj); err != nil {
			return nil, err
		}
		return obj, nil
	case "RayCluster":
		obj := &rayv1.RayCluster{}
		if err := yaml.Unmarshal([]byte(yamlManifest), obj); err != nil {
			return nil, err
		}
		return obj, nil
	case "AppWrapper":
		obj := &appwrapperv1beta2.AppWrapper{}
		if err := yaml.Unmarshal([]byte(yamlManifest), obj); err != nil {
			return nil, err
		}
		return obj, nil
	case "EventBus":
		obj := &eventsv1alpha1.EventBus{}
		if err := yaml.Unmarshal([]byte(yamlManifest), obj); err != nil {
			return nil, err
		}
		return obj, nil
	default:
		obj := &unstructured.Unstructured{}
		if err := yaml.Unmarshal([]byte(yamlManifest), obj); err != nil {
			return nil, err
		}
		return obj, nil
	}
}

func ToUnstructuredObject(obj runtime.Object) (*unstructured.Unstructured, error) {
	var unstructuredObj *unstructured.Unstructured
	if u, ok := obj.(*unstructured.Unstructured); ok {
		unstructuredObj = u
	} else {
		data, err := json.Marshal(obj)
		if err != nil {
			zlog.SugarErrorf("Failed to marshal object to JSON: %v", err)
			return nil, err
		}
		unstructuredObj = &unstructured.Unstructured{}
		if err := json.Unmarshal(data, unstructuredObj); err != nil {
			zlog.SugarErrorf("Failed to unmarshal object to Unstructured: %v", err)
			return nil, err
		}
	}

	return unstructuredObj, nil
}

func CleanupRuntimeFieldsFromRuntimeObject(obj runtime.Object) (*unstructured.Unstructured, error) {
	unstructuredObj, err := ToUnstructuredObject(obj)
	if err != nil {
		return nil, err
	}
	return CleanupRuntimeFields(unstructuredObj), nil
}

func CleanupRuntimeFields(obj *unstructured.Unstructured) *unstructured.Unstructured {
	// Deep copy the object to avoid modifying the original
	cleanObj := obj.DeepCopy()

	unstructured.RemoveNestedField(cleanObj.Object, "metadata", "uid")
	unstructured.RemoveNestedField(cleanObj.Object, "metadata", "resourceVersion")
	unstructured.RemoveNestedField(cleanObj.Object, "metadata", "creationTimestamp")
	unstructured.RemoveNestedField(cleanObj.Object, "metadata", "generation")
	unstructured.RemoveNestedField(cleanObj.Object, "metadata", "managedFields")
	unstructured.RemoveNestedField(cleanObj.Object, "metadata", "annotations", "kubectl.kubernetes.io/last-applied-configuration")

	// clean status
	unstructured.RemoveNestedField(cleanObj.Object, "status")

	return cleanObj
}

// CleanK8sRuntimeInfo removes Kubernetes runtime information from an object
// to create a clean spec-only version suitable for YAML export.
// This function removes server-generated fields like resourceVersion, uid,
// generation, managedFields, status, etc.
func CleanK8sRuntimeInfo(obj runtime.Object) runtime.Object {
	// Deep copy the object to avoid modifying the original
	cleanObj := obj.DeepCopyObject()

	// Get the meta object to clean metadata
	if metaObj, ok := cleanObj.(metav1.Object); ok {
		// Create clean ObjectMeta keeping only essential fields
		cleanMeta := metav1.ObjectMeta{
			Name:        metaObj.GetName(),
			Namespace:   metaObj.GetNamespace(),
			Labels:      copyStringMap(metaObj.GetLabels()),
			Annotations: copyStringMap(metaObj.GetAnnotations()),
		}

		// Set GenerateName if Name is empty (for template objects)
		if cleanMeta.Name == "" && metaObj.GetGenerateName() != "" {
			cleanMeta.GenerateName = metaObj.GetGenerateName()
		}

		// Set the cleaned metadata
		metaObj.SetName(cleanMeta.Name)
		metaObj.SetNamespace(cleanMeta.Namespace)
		metaObj.SetLabels(cleanMeta.Labels)
		metaObj.SetAnnotations(cleanMeta.Annotations)
		metaObj.SetGenerateName(cleanMeta.GenerateName)

		// Clear runtime-generated fields
		metaObj.SetResourceVersion("")
		metaObj.SetUID("")
		metaObj.SetGeneration(0)
		metaObj.SetSelfLink("")
		metaObj.SetCreationTimestamp(metav1.Time{})
		metaObj.SetDeletionTimestamp(nil)
		metaObj.SetDeletionGracePeriodSeconds(nil)
		metaObj.SetOwnerReferences(nil)
		metaObj.SetFinalizers(nil)
		metaObj.SetManagedFields(nil)
	}

	// Clear status field using reflection for different CRD types
	cleanStatus(cleanObj)

	return cleanObj
}

// copyStringMap creates a deep copy of a string map
func copyStringMap(original map[string]string) map[string]string {
	if original == nil {
		return nil
	}
	copy := make(map[string]string, len(original))
	for k, v := range original {
		copy[k] = v
	}
	return copy
}

// cleanStatus removes the status field from Kubernetes objects using reflection
func cleanStatus(obj runtime.Object) {
	objValue := reflect.ValueOf(obj)
	if objValue.Kind() == reflect.Ptr {
		objValue = objValue.Elem()
	}

	if objValue.Kind() != reflect.Struct {
		return
	}

	// Look for Status field and set it to zero value
	statusField := objValue.FieldByName("Status")
	if statusField.IsValid() && statusField.CanSet() {
		statusField.Set(reflect.Zero(statusField.Type()))
	}
}
