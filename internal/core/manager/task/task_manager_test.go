package task

import (
	"context"
	"fmt"
	"testing"
	"time"

	"transwarp.io/mlops/pipeline/internal/core/manager/task/cache"
	model "transwarp.io/mlops/pipeline/internal/core/model/task"
)

func TestGetComponents(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping long-running integration test in short mode")
	}

	ts := NewTaskManager()
	req := &model.ListTasksReq{}
	resp, err := ts.ListTasks(context.Background(), req)
	if err != nil {
		fmt.Println(err.Error())
	} else {
		fmt.Println(len(resp.Items))
	}
}

func TestTaskForest(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping long-running integration test in short mode")
	}

	tc := cache.NewTaskSnapshot()
	tc.Start(context.Background())
	time.Sleep(5 * time.Second)
	task, err := tc.ListTasks(context.Background())
	if err != nil {
		t.Error(err)
	}
	if task == nil {
		t.Error("task is nil")
	}
}

func TestTaskFromYaml(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping long-running integration test in short mode")
	}

	// Example YAML manifest. This could also be read from a file.
	yamlManifest := `
apiVersion: v1
kind: Namespace
metadata:
  name: my-go-app-namespace
  labels:
    app.kubernetes.io/managed-by: go-apply-example
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-deployment
  namespace: my-go-app-namespace
spec:
  replicas: 2
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      containers:
      - name: nginx
        image: nginx:1.14.2
        ports:
        - containerPort: 80
`

	fmt.Println(yamlManifest)
	// tya, err := NewTaskYamlApplier()
	// if err != nil {
	// 	t.Error(err)
	// }
	// err = tya.ApplyFromYaml(context.Background(), yamlManifest)
	// if err != nil {
	// 	t.Error(err)
	// }
}
