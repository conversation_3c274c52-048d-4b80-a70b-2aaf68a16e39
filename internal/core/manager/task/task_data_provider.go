package task

import (
	"context"

	taskcache "transwarp.io/mlops/pipeline/internal/core/manager/task/cache"
	model "transwarp.io/mlops/pipeline/internal/core/model/task"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
)

type TaskDataProvider struct {
	taskSnapshot *taskcache.TaskSnapshot
}

func NewTaskDataProvider(kueueClient *client.KueueClient) *TaskDataProvider {
	taskSnapshot := taskcache.NewTaskSnapshot()
	if err := taskSnapshot.Start(context.Background()); err != nil {
		panic(err)
	}
	return &TaskDataProvider{
		taskSnapshot: taskSnapshot,
	}
}

func (tdp *TaskDataProvider) GetTask(ctx context.Context, id string) (*model.Task, error) {
	return tdp.taskSnapshot.GetTask(ctx, id)
}

func (tdp *TaskDataProvider) GetData(ctx context.Context) ([]*model.Task, error) {
	return tdp.taskSnapshot.ListTasks(ctx)
}

func (tdp *TaskDataProvider) Count(ctx context.Context) (int32, error) {
	tasks, err := tdp.taskSnapshot.ListTasks(ctx)
	if err != nil {
		return 0, err
	}
	return int32(len(tasks)), nil
}

func (tdp *TaskDataProvider) DeleteTask(ctx context.Context, id string) error {
	return tdp.taskSnapshot.DeleteTask(ctx, id)
}

func (tdp *TaskDataProvider) StopTask(ctx context.Context, id string) error {
	return tdp.taskSnapshot.StopTask(ctx, id)
}
func (tdp *TaskDataProvider) ResumeTask(ctx context.Context, id string) error {
	return tdp.taskSnapshot.ResumeTask(ctx, id)
}
