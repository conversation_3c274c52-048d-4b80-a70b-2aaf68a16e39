package run

import (
	"github.com/argoproj/argo-workflows/v3/pkg/apis/workflow/v1alpha1"
	"k8s.io/apimachinery/pkg/runtime"
	"time"
	framework "transwarp.io/mlops/pipeline/internal/core/manager/task/handlerframework"
	"transwarp.io/mlops/pipeline/internal/dao"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
	"transwarp.io/mlops/pipeline/internal/util"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

var (
	gvk = v1alpha1.SchemeGroupVersion.WithKind("Workflow")
)

const (
	FrameworkName = "workflow"
)

type WorkflowHandler struct {
	*framework.BaseHandler
}

func init() {
	handler := NewWorkflowHandler(
		framework.NewBaseHandler(
			client.MustGetArgoWorkflowClient().WorkflowInformer,
		),
	)

	framework.RegisterHandler(FrameworkName, handler)
}

func NewWorkflowHandler(baseHandler *framework.BaseHandler) *WorkflowHandler {
	return &WorkflowHandler{
		BaseHandler: baseHandler,
	}
}

func (h *WorkflowHandler) Name() string {
	return FrameworkName
}

func (h *WorkflowHandler) ShouldHandle(obj runtime.Object) bool {
	if !h.BaseHandler.ShouldHandle(obj) {
		return false
	}

	workflow, ok := obj.(*v1alpha1.Workflow)
	if !ok {
		return false
	}

	labels := workflow.GetLabels()
	if labels == nil {
		return false
	}
	for k, v := range util.CustomLabels() {
		if labels[k] != v {
			return false
		}
	}

	return true
}

func (h *WorkflowHandler) HandleResourceCreate(obj runtime.Object) {
	argoWkf, ok := obj.(*v1alpha1.Workflow)
	if !ok {
		zlog.SugarErrorf("failed to convert workflow, %s = %s", "object", obj)
		return
	}
	workflow := NewWorkflow(argoWkf)
	run := workflow.ToModel()
	err := dao.RunDAOInst.Save(run)
	if err != nil {
		zlog.SugarErrorf("failed to save run, %s = %s, %s = %s", "workflow", workflow.Name, "run", run.UUID)
	}
}

func (h *WorkflowHandler) HandleResourceUpdate(oldObj, newObj runtime.Object) {
	newArgoWkf, ok := newObj.(*v1alpha1.Workflow)
	if !ok {
		zlog.SugarErrorf("failed to convert workflow, %s = %s", "new object", newObj)
		return
	}
	workflow := NewWorkflow(newArgoWkf)
	run := workflow.ToModel()
	err := dao.RunDAOInst.Update(run.UUID, run.Conditions, run.FinishedAtInSec, run.WorkflowRuntimeManifest)
	if err != nil {
		zlog.SugarErrorf("failed to update run", "workflow", workflow.Name, "run", run.UUID, "err", err)
	}
}

func (h *WorkflowHandler) HandleResourceDelete(obj runtime.Object) {
	argoWkf, ok := obj.(*v1alpha1.Workflow)
	if !ok {
		zlog.SugarErrorf("failed to convert workflow, %s = %s", "object", obj)
		return
	}
	workflow := NewWorkflow(argoWkf)
	run := workflow.ToModel()

	if run.FinishedAtInSec == 0 {
		run.FinishedAtInSec = time.Now().Unix()
		run.Conditions = "Failed"
		err := dao.RunDAOInst.Update(run.UUID, run.Conditions, run.FinishedAtInSec, run.WorkflowRuntimeManifest)
		if err != nil {
			zlog.SugarErrorf("failed to update run", "workflow", workflow.Name, "run", run.UUID, "err", err)
		}
	}
}
