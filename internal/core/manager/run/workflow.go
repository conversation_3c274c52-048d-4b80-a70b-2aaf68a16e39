package run

import (
	"encoding/json"
	"github.com/argoproj/argo-workflows/v3/pkg/apis/workflow/v1alpha1"
	"github.com/golang/glog"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"strconv"
	"transwarp.io/mlops/pipeline/internal/core/consts"
	"transwarp.io/mlops/pipeline/internal/dao/model"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

// Workflow is a type to help manipulate Workflow objects.
type Workflow struct {
	*v1alpha1.Workflow
}

// NewWorkflow creates a Workflow.
func NewWorkflow(workflow *v1alpha1.Workflow) *Workflow {
	return &Workflow{
		workflow,
	}
}

func (w *Workflow) ToModel() *model.Run {
	run := &model.Run{
		UUID:                    w.GetRunId(),
		Name:                    w.Name,
		Namespace:               w.Namespace,
		CreatedAtInSec:          w.CreationTimestamp.Unix(),
		ScheduledAtInSec:        w.ScheduledAtInSec(),
		FinishedAtInSec:         w.Finished<PERSON>t(),
		Conditions:              w.Condition(),
		WorkflowSpecManifest:    w.GetWorkflowSpec().ToStringForStore(),
		WorkflowRuntimeManifest: w.ToStringForStore(),
		ProjectID:               w.Labels[consts.LabelProjectId],
		SourceType:              w.Labels[consts.LabelTaskRefSourceTypeKey],
		Owner:                   w.Labels[consts.LabelTaskRefSourceIDKey],
		SourceID:                w.Labels[consts.LabelTaskRefSourceIDKey],
		SourceName:              w.Annotations[consts.AnnotationTaskNameKey],
	}
	return run
}

func (w *Workflow) ScheduledAtInSec() int64 {
	if w.Annotations == nil {
		return 0
	}
	scheduledTime, err := strconv.ParseInt(w.GetAnnotations()[consts.AnnotationWorkflowScheduledTime], 10, 64)
	if err != nil {
		zlog.SugarDebugf("failed to parse int, %s = %s", consts.AnnotationWorkflowScheduledTime, w.GetAnnotations()[consts.AnnotationWorkflowScheduledTime])
		scheduledTime = 0
	}
	return scheduledTime
}

func (w *Workflow) Condition() string {
	return string(w.Status.Phase)
}

func (w *Workflow) GetRunId() string {
	uuid := w.Annotations[consts.AnnotationWorkflowRunId]
	if uuid == "" {
		uuid = string(w.UID)
	}
	return uuid
}

func (w *Workflow) FinishedAt() int64 {
	if w.Status.FinishedAt.IsZero() {
		// If workflow is not finished
		return 0
	}
	return w.Status.FinishedAt.Unix()
}

func (w *Workflow) GetWorkflowSpec() *Workflow {
	workflow := w.DeepCopy()
	workflow.Status = v1alpha1.WorkflowStatus{}
	workflow.TypeMeta = metav1.TypeMeta{Kind: w.Kind, APIVersion: w.APIVersion}
	// To prevent collisions, clear name, set GenerateName to first 200 runes of previous name.
	nameRunes := []rune(w.Name)
	length := len(nameRunes)
	if length > 200 {
		length = 200
	}
	workflow.ObjectMeta = metav1.ObjectMeta{GenerateName: string(nameRunes[:length])}
	return NewWorkflow(workflow)
}

func (w *Workflow) ToStringForStore() string {
	workflow, err := json.Marshal(w.Workflow)
	if err != nil {
		glog.Errorf("Could not marshal the workflow: %v", w.Workflow)
		return ""
	}
	return string(workflow)
}
