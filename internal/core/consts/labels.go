package consts

const (
	LabelKeyCacheEnabled = "pipelines.kubeflow.org/cache_enabled"

	// const belong llmops product
	// value is llmops installed namespace
	LabelLLMOpsProductKey = "llmops.transwarp.io/product"

	LabelLLMOpsPartOfKey = "llmops.transwarp.io/part-of"

	LabelLLMOpsComponentKey = "llmops.transwarp.io/component"

	// value is llmops installed namespace
	LabelLLMOpsManagedByKey = "llmops.transwarp.io/managed-by"

	LabelLLMOpsVersionKey = "llmops.transwarp.io/version"

	// task label
	LabelTaskManagedByKey = "task.transwarp.io/managed-by"

	// kueue related label
	LabelKueueQueueNameKey   = "kueue.x-k8s.io/queue-name"
	LabelKueueQueueNameValue = "task"
	// [ns]-[high|medium|low], ex: llmops-high  llmops-low  llmops-medium
	LabelKueueWorkloadPriorityKey    = "kueue.x-k8s.io/priority-class"
	LabelKueueWorkloadPriorityHigh   = "high"
	LabelKueueWorkloadPriorityLow    = "low"
	LabelKueueWorkloadPriorityMedium = "medium"

	LabelKueuePodGroupNameKey = "kueue.x-k8s.io/pod-group-name"
	LabelKueueManagedKey      = "kueue.x-k8s.io/managed"
	LabelKueuePodsetKey       = "kueue.x-k8s.io/podset"

	LabelProjectId            = "task.transwarp.io/project-id"
	LabelTaskRefSourceIDKey   = "task.transwarp.io/ref-source-id"
	LabelTaskRefSourceTypeKey = "task.transwarp.io/ref-source-type"
	LabelTaskRefRunIDKey      = "task.transwarp.io/ref-run-id"
	LabelTaskRefRunNameKey    = "task.transwarp.io/ref-run-name"

	// workflow
	LabelWorkflowCompletedKey = "workflows.argoproj.io/completed"
	LabelWorkflowPhaseKey     = "workflows.argoproj.io/phase"
)

const (
	LabelLLMOpsProductValue   = "llmops"
	LabelLLMOpsComponentValue = "pipeline"

	LabelTrueValue  = "true"
	LabelFalseValue = "false"
)

func ContainLabels(target, subset map[string]string) bool {
	if target == nil || subset == nil {
		return false
	}

	for key, value := range subset {
		targetValue, exists := target[key]
		if !exists || targetValue != value {
			return false
		}
	}
	return true
}

func HasKueueQueueNameLabel(labels map[string]string) bool {
	if v, ok := labels[LabelKueueQueueNameKey]; ok && v != "" {
		return true
	}

	return false
}

func HasKueueManagedLabel(labels map[string]string) bool {
	if v, ok := labels[LabelKueueManagedKey]; ok && v == LabelTrueValue {
		return true
	}

	return false
}

func HasKueuePodsetLabel(labels map[string]string) bool {
	if _, ok := labels[LabelKueuePodsetKey]; ok {
		return true
	}

	return false
}
