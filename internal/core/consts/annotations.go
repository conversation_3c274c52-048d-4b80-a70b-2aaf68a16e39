package consts

const (
	AnnotationTaskManagedByKey = "task.transwarp.io/managed-by"

	// for task
	AnnotationTaskIDKey          = "task.transwarp.io/id"
	AnnotationTaskNameKey        = "task.transwarp.io/name"
	AnnotationTaskTypeKey        = "task.transwarp.io/type"
	AnnotationTaskDescriptionKey = "task.transwarp.io/description"
	AnnotationTaskCreatorKey     = "task.transwarp.io/creator"

	AnnotationTaskCreateAtKey = "task.transwarp.io/created-at"
	AnnotationTaskUpdateAtKey = "task.transwarp.io/updated-at"
	AnnotationTaskStartAtKey  = "task.transwarp.io/started-at"
	AnnotationTaskEndAtKey    = "task.transwarp.io/ended-at"

	AnnotationTaskTenantIDKey    = "task.transwarp.io/tenant-id"
	AnnotationTaskTenantNameKey  = "task.transwarp.io/tenant-name"
	AnnotationTaskProjectIDKey   = "task.transwarp.io/project-id"
	AnnotationTaskProjectNameKey = "task.transwarp.io/project-name"

	AnnotationTaskRefSourceIDKey   = "task.transwarp.io/ref-source-id"
	AnnotationTaskRefSourceNameKey = "task.transwarp.io/ref-source-name"
	AnnotationTaskRefRunIDKey      = "task.transwarp.io/ref-run-id"
	AnnotationTaskRefRunNameKey    = "task.transwarp.io/ref-run-name"

	AnnotationTaskExtraKey = "task.transwarp.io/extra"

	// kueue
	AnnotationKueuePodSuspendingParentKey = "kueue.x-k8s.io/pod-suspending-parent"
	AnnotationKueuePodGroupTotalCountKey  = "kueue.x-k8s.io/pod-group-total-count"

	// workflow
	AnnotationWorkflowScheduledTime = "workflows.argoproj.io/scheduled-time"
	AnnotationWorkflowRunId         = "task.transwarp.io/run-id"
)
