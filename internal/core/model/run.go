package model

import (
	"context"
	"fmt"
	"github.com/argoproj/argo-workflows/v3/pkg/apis/workflow/v1alpha1"
	"strings"
	pb "transwarp.io/aip/llmops-common/pb/pipeline"
	"transwarp.io/applied-ai/aiot/vision-std/boot/k8s"
	"transwarp.io/mlops/mlops-std/util"
	"transwarp.io/mlops/pipeline/internal/core/consts"
	"transwarp.io/mlops/pipeline/internal/dao/model"
)

const (
	META_PIPELINE_ID         = "pipeline_id"
	META_PIPELINE_VERSION_ID = "pipeline_version_id"
	META_PROJECT_ID          = "project_id"
	META_TENANT_ID           = "tenant_id"
	META_TASK_TYPE           = "task_type"
	META_TASK_SOURCE_ID      = "task_source_id"
	META_TASK_SOURCE_NAME    = "task_source_name"
	META_TASK_SOURCE_INFO    = "task_source_info"
	META_MLOPS_NAMESPACE     = "mlops_namespace"
	META_SOPHON_Id           = "sophon_id"
)

type Run struct {
	Id            string
	ProjectId     string
	TaskSource    *pb.TaskSource
	TaskFlow      *TaskFlow
	StartTime     int64
	EndTime       int64
	Status        pb.Run_Status
	ScheduledTime int64
	Steps         []Step
	CreateUser    string
	Desc          string
}

func (run *Run) ToWorkflow(ctx context.Context, tenantUID string) (*v1alpha1.Workflow, error) {
	labels := run.ToLabels()
	annotations, err := run.ToAnnotations(tenantUID)
	if err != nil {
		return nil, err
	}
	return run.TaskFlow.ToWorkflow(ctx, strings.ReplaceAll(run.TaskSource.GetSourceId(), "/", ""), run.ProjectId, labels, annotations)
}

func (run *Run) ToLabels() map[string]string {
	labels := map[string]string{
		consts.LabelLLMOpsProductKey:         consts.LabelLLMOpsProductValue,
		consts.LabelLLMOpsComponentKey:       consts.LabelLLMOpsComponentValue,
		consts.LabelLLMOpsManagedByKey:       k8s.CurrentNamespaceInCluster(),
		consts.LabelProjectId:                run.ProjectId,
		consts.LabelTaskRefSourceIDKey:       run.TaskSource.SourceId,
		consts.LabelTaskRefSourceTypeKey:     run.TaskSource.SourceType.String(),
		consts.LabelTaskManagedByKey:         k8s.CurrentNamespaceInCluster(),
		consts.LabelKueueQueueNameKey:        consts.LabelKueueQueueNameValue,
		consts.LabelKueueWorkloadPriorityKey: fmt.Sprintf("%s-%s", k8s.CurrentNamespaceInCluster(), consts.LabelKueueWorkloadPriorityMedium),
	}
	return labels
}

func (run *Run) ToAnnotations(tenantUID string) (map[string]string, error) {
	annotations := map[string]string{
		consts.AnnotationTaskRefSourceNameKey: run.TaskSource.SourceName,
		consts.AnnotationTaskRefSourceIDKey:   run.TaskSource.SourceId,
		consts.AnnotationTaskNameKey:          run.TaskSource.SourceName,
		consts.AnnotationTaskTypeKey:          run.TaskSource.SourceType.String(),
		consts.AnnotationTaskDescriptionKey:   run.Desc,
		consts.AnnotationTaskCreatorKey:       run.CreateUser,
		consts.AnnotationTaskTenantIDKey:      tenantUID,
		consts.AnnotationTaskProjectIDKey:     run.ProjectId,
	}
	return annotations, nil
}

func (run *Run) ToPb() *pb.Run {
	steps := make([]*pb.Step, 0)
	for _, s := range run.Steps {
		steps = append(steps, s.ToPb())
	}
	return &pb.Run{
		Id:            run.Id,
		ProjectId:     run.ProjectId,
		TaskSource:    run.TaskSource,
		TaskFlow:      run.TaskFlow.ToPb(),
		StartTime:     run.StartTime,
		EndTime:       run.EndTime,
		Status:        run.Status,
		ScheduledTime: run.ScheduledTime,
		Steps:         steps,
		CreateUser:    run.CreateUser,
		Desc:          run.Desc,
	}
}

func (run *Run) ToPbV2() *pb.RunV2 {
	steps := make([]*pb.Step, 0, len(run.Steps))
	for _, s := range run.Steps {
		steps = append(steps, s.ToPb())
	}
	return &pb.RunV2{
		Id:            run.Id,
		ProjectId:     run.ProjectId,
		TaskSource:    run.TaskSource,
		TaskFlow:      run.TaskFlow.ToPbV2(),
		StartTime:     run.StartTime,
		EndTime:       run.EndTime,
		Status:        pb.RunV2_Status(run.Status),
		ScheduledTime: run.ScheduledTime,
		Steps:         steps,
		CreateUser:    run.CreateUser,
		Desc:          run.Desc,
	}
}

func (run *Run) FromEntry(entry model.Run) *Run {
	var workflow v1alpha1.Workflow
	util.FromJson(entry.WorkflowRuntimeManifest, &workflow)
	var steps []Step
	var taskFlow TaskFlow
	util.FromJson(workflow.Annotations[MetaTaskFlow], &taskFlow)
	nodeMap := nodes2Map(taskFlow.Nodes)

	for _, node := range workflow.Status.Nodes {
		if node.Type == v1alpha1.NodeTypePod {
			nodeName := node.TemplateName
			runNode := nodeMap[node.TemplateName]
			if runNode != nil {
				nodeName = runNode.Name
			}
			step := &Step{}
			step.FromWorkflow(&node, nodeName)
			if step.Phase == pb.Step_Failed {
				step.Phase = pb.Step_Error
			}
			steps = append(steps, *step)
		}
	}

	run.Id = entry.UUID
	run.CreateUser = entry.Owner
	run.ProjectId = entry.ProjectID
	run.TaskSource = &pb.TaskSource{
		SourceType: pb.TaskSourceType(pb.TaskSourceType_value[entry.SourceType]),
		SourceId:   entry.SourceID,
		SourceName: entry.SourceName,
		SourceInfo: workflow.Annotations[META_TASK_SOURCE_INFO],
	}

	run.TaskFlow = &taskFlow
	condition, ok := pb.Run_Status_value[entry.Conditions]
	if ok {
		run.Status = pb.Run_Status(condition)
	} else {
		run.Status = pb.Run_Running
	}
	run.StartTime = entry.CreatedAtInSec
	run.EndTime = entry.FinishedAtInSec
	run.Steps = steps
	run.ScheduledTime = 0

	if run.EndTime != 0 && run.StartTime != 0 {
		run.ScheduledTime = run.EndTime - run.StartTime
	}
	return run
}

func nodes2Map(nodes []*Node) map[string]*Node {
	nodeMap := map[string]*Node{}
	for _, node := range nodes {
		nodeMap[node.Id] = node
	}
	return nodeMap
}

type RunDesc struct {
	SophonId   string
	ProjectId  string
	Owner      string
	SourceId   string
	SourceType string
	SourceName string
}
