package model

import (
	"context"
	"fmt"
	"sort"
	"strings"

	eventsv1alpha1 "github.com/argoproj/argo-events/pkg/apis/events/v1alpha1"
	"github.com/argoproj/argo-workflows/v3/pkg/apis/workflow/v1alpha1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/utils/ptr"
	pb "transwarp.io/aip/llmops-common/pb/pipeline"
	"transwarp.io/mlops/mlops-std/stderr"
	_const "transwarp.io/mlops/pipeline/const"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
)

type RuntimeResources struct {
	CronWorkflows map[string]*v1alpha1.CronWorkflow
	Sensors       map[string]*eventsv1alpha1.Sensor
}

func (version *PipelineVersion) ToRuntimeResources(ctx context.Context, pipeline *Pipeline) (*RuntimeResources, error) {
	//init
	cronWorkflows := make(map[string]*v1alpha1.CronWorkflow)
	sensors := make(map[string]*eventsv1alpha1.Sensor)
	project, err := client.MustGetCASHTTPClient().GetProjectDetailById(ctx, pipeline.ProjectId)
	if err != nil {
		return nil, err
	}
	workflow, err := version.ToWorkflow(ctx, pipeline, project.TenantUID)
	if err != nil {
		return nil, err
	}
	switch version.SchedulingStyle {
	case pb.PipelineVersion_TIMING:
		cronWorkflow, err := version.ToCronWorkflow(workflow, project.TenantUID)
		if err != nil {
			return nil, err
		}
		cronWorkflows[cronWorkflow.Name] = cronWorkflow
	case pb.PipelineVersion_EVENT:
		sensor, err := version.ToEventSensor(ctx, workflow, project.TenantUID)
		if err != nil {
			return nil, err
		}
		sensors[sensor.Name] = sensor
	default:
		return nil, stderr.Internal.Errorf("SchedulingStyle %s is not support", version.SchedulingStyle.String())
	}
	return &RuntimeResources{
		CronWorkflows: cronWorkflows,
		Sensors:       sensors,
	}, nil

}

func ToCrontab(cron string) (string, error) {
	parts := strings.Fields(cron)
	if len(parts) == 5 {
		return cron, nil
	}
	if len(parts) < 6 || len(parts) > 7 {
		return "", stderr.Internal.Errorf("cron表达式异常: %s", cron)
	}

	// 丢弃秒字段
	parts = parts[1:]

	// 丢弃年字段（如果有）
	if len(parts) == 6 {
		parts = parts[:5]
	}

	// 替换 ? 为 *
	for i, p := range parts {
		if p == "?" {
			parts[i] = "*"
		}
		// 简单检查不支持的语法
		if strings.ContainsAny(p, "L#") {
			return "", stderr.Internal.Errorf("不支持的 Quartz 语法: %s", p)
		}
	}

	return strings.Join(parts, " "), nil
}
func (version *PipelineVersion) ToCronWorkflow(workflow *v1alpha1.Workflow, tenantUid string) (*v1alpha1.CronWorkflow, error) {
	cron, err := ToCrontab(version.TimingConfig.Cron)
	if err != nil {
		return nil, err
	}
	tarSensor := &v1alpha1.CronWorkflow{
		ObjectMeta: metav1.ObjectMeta{
			Name:        GenName(version.Id),
			Namespace:   tenantUid,
			Labels:      workflow.Labels,
			Annotations: workflow.Annotations,
		},
		Spec: v1alpha1.CronWorkflowSpec{
			Schedule:               cron,
			Timezone:               "Asia/Shanghai",
			ConcurrencyPolicy:      v1alpha1.AllowConcurrent,
			FailedJobsHistoryLimit: ptr.To(int32(10)),
			WorkflowSpec:           workflow.Spec,
			WorkflowMetadata: &metav1.ObjectMeta{
				Labels:      workflow.Labels,
				Annotations: workflow.Annotations,
			},
		},
	}

	return tarSensor, nil
}

func GenName(id string) string {
	return _const.PrefixName + id
}
func (version *PipelineVersion) ToEventSensor(ctx context.Context, workflow *v1alpha1.Workflow, tenantUid string) (*eventsv1alpha1.Sensor, error) {
	taskId := strings.Split(version.EventConfig.FileAssetsParam.TaskTemplateId, "/")[1]
	task, err := client.MustGetCVHTTPClient().GetTaskDetail(ctx, taskId)
	if err != nil {
		return nil, err
	}
	dependencyName := "redis-event"
	workflowResource := eventsv1alpha1.NewK8SResource(workflow)
	var catalogLua string
	if len(task.CatalogParams.CatalogID) > 0 {
		catalogLua = fmt.Sprintf("contains(event.body.systemIds, \"%s\")", task.CatalogParams.CatalogID)
	} else {
		catalogLua = fmt.Sprintf("#event.body.systemIds==0")
	}
	lua := fmt.Sprintf(`
function unordered_arrays_equal(a, b)
  if type(a) ~= "table" or type(b) ~= "table" then
    return false
  end

  if #a ~= #b then
    return false
  end

  local countA = {}
  for _, v in ipairs(a) do
    countA[v] = (countA[v] or 0) + 1
  end

  for _, v in ipairs(b) do
    if not countA[v] then
      return false
    end
    countA[v] = countA[v] - 1
    if countA[v] < 0 then
      return false
    end
  end

  for _, v in pairs(countA) do
    if v ~= 0 then
      return false
    end
  end

  return true
end
function contains(arr, value)
  if type(arr) ~= "table" then return false end

  for _, v in ipairs(arr) do
    if v == value then
      return true
    end
  end
  return false
end
return event.body.type == "file-assets"
  and (event.body.operation == "re-parsed")
  and unordered_arrays_equal(event.body.labelIds, %s)
  and %s
`, intArrayToLua(task.CatalogParams.LabelIDs), catalogLua)
	tarSensor := &eventsv1alpha1.Sensor{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Sensor",
			APIVersion: "argoproj.io/v1alpha1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:        GenName(version.Id),
			Namespace:   tenantUid,
			Labels:      workflow.Labels,
			Annotations: workflow.Annotations,
		},
		Spec: eventsv1alpha1.SensorSpec{
			Template: &eventsv1alpha1.Template{
				ServiceAccountName: PipelineServiceAccount,
			},
			Dependencies: []eventsv1alpha1.EventDependency{
				{
					Name:            dependencyName,
					EventSourceName: strings.ToLower(version.EventConfig.Type.String()),
					EventName:       "message",
					Filters: &eventsv1alpha1.EventDependencyFilter{
						Script: lua,
					},
				},
			},
			Triggers: []eventsv1alpha1.Trigger{
				{
					Template: &eventsv1alpha1.TriggerTemplate{
						Name: workflow.GenerateName,
						ArgoWorkflow: &eventsv1alpha1.ArgoWorkflowTrigger{
							Operation: eventsv1alpha1.Submit,
							Parameters: []eventsv1alpha1.TriggerParameter{
								{
									Src: &eventsv1alpha1.TriggerParameterSource{
										DependencyName: dependencyName,
										DataKey:        "body.assetId",
									},
									Dest: "spec.arguments.parameters.0.value",
								}, {
									Src: &eventsv1alpha1.TriggerParameterSource{
										DependencyName: dependencyName,
										DataKey:        "body.versionId",
									},
									Dest: "spec.arguments.parameters.1.value",
								},
							},
							Source: &eventsv1alpha1.ArtifactLocation{
								Resource: &workflowResource,
							},
						},
					},
				},
			},
		},
	}

	return tarSensor, nil
}

func intArrayToLua(array []int) string {
	sort.Ints(array)

	strs := make([]string, len(array))
	for i, v := range array {
		strs[i] = fmt.Sprintf("%d", v)
	}

	return fmt.Sprintf("{%s}", strings.Join(strs, ", "))
}
