package task

type ListTasksReq struct {
	Page     int32 `json:"page"`
	PageSize int32 `json:"page_size"`

	QueueName string `json:"queue_name"`

	TenantIDs []string `json:"tenant_id"`
	Status    []string `json:"status" form:"status"`
	// mult
	ProjectIDs []string `json:"project_id"`
	Types      []string `json:"type" form:"type"`
	Priorities []int32  `json:"priority" form:"priority"`

	// 时间范围
	CreatedAtStart *int64 `json:"created_at_start"`
	CreatedAtEnd   *int64 `json:"created_at_end"`

	// 搜索关键词
	SearchKeyword string `json:"search_keyword" form:"search_keyword"`

	// 排序参数
	SortBy    string `json:"sort_by" form:"sort_by"`
	SortOrder string `json:"sort_order" form:"sort_order"`
}

type ListTasksResp struct {
	Items      []*Task `json:"items"`
	Total      int32   `json:"total"`
	Page       int32   `json:"page"`
	PageSize   int32   `json:"page_size"`
	TotalPages int32   `json:"total_pages"`
}
