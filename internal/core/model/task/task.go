package task

import (
	"sort"
	"strings"
	"time"

	corev1 "k8s.io/api/core/v1"

	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/apimachinery/pkg/runtime/schema"
	kueuev1beta1 "sigs.k8s.io/kueue/apis/kueue/v1beta1"
)

const (
	Zero = "0"
)

type Task struct {
	ID string `json:"id"`

	Name        string       `json:"name"`
	Type        TaskType     `json:"type"`
	Priority    TaskPriority `json:"priority"`
	Description string       `json:"description"`
	Creator     string       `json:"creator"`
	CreatedAt   time.Time    `json:"created_at"`
	UpdatedAt   time.Time    `json:"updated_at"`
	Status      TaskStatus   `json:"status"`
	StartedAt   *time.Time   `json:"started_at"`
	EndedAt     *time.Time   `json:"ended_at"`

	TenantID    string `json:"tenant_id"`
	TenantName  string `json:"tenant_name"`
	ProjectID   string `json:"project_id"`
	ProjectName string `json:"project_name"`

	YamlRawManifest string `json:"-"`
	RefSourceID     string `json:"ref_source_id"`
	RefRunID        string `json:"ref_run_id"`

	Extra map[string]string `json:"extra"`

	Resources      *Resources    `json:"resources"`
	NodeNames      []string      `json:"node_names"`
	SubTasks       []*SubTask    `json:"sub_tasks"`
	Rank           int           `json:"rank"`
	PodCount       int           `json:"pod_count"`
	QueuedDuration time.Duration `json:"queued_duration"`

	QueueName        string `json:"queue_name"`
	ClusterQueueName string `json:"cluster_queue_name"`

	Namespace   string                      `json:"-"`
	Labels      map[string]string           `json:"-"`
	Annotations map[string]string           `json:"-"`
	GVK         schema.GroupVersionKind     `json:"gvk"`
	GVR         schema.GroupVersionResource `json:"-"`
}

type XPUResource struct {
	Name   string  `json:"name"`
	Count  float64 `json:"count"`
	Cores  int64   `json:"cores"`
	Memory string  `json:"memory"`
}

type ResourceSpec struct {
	CPU    string         `json:"cpu"`
	Memory string         `json:"memory"`
	XPU    []*XPUResource `json:"xpu"`
}

func NewResourceSpec() *ResourceSpec {
	return &ResourceSpec{
		CPU:    Zero,
		Memory: Zero,
		XPU:    make([]*XPUResource, 0),
	}
}

type Resources struct {
	Requests ResourceSpec  `json:"requests"`
	Limits   ResourceSpec  `json:"limits"`
	Used     *ResourceSpec `json:"used"`
}

func (r *Resources) Add(other *Resources) {
	if other == nil {
		return
	}

	// Add CPU and Memory
	r.Requests.CPU = addResourceQuantity(r.Requests.CPU, other.Requests.CPU)
	r.Requests.Memory = addResourceQuantity(r.Requests.Memory, other.Requests.Memory)
	r.Limits.CPU = addResourceQuantity(r.Limits.CPU, other.Limits.CPU)
	r.Limits.Memory = addResourceQuantity(r.Limits.Memory, other.Limits.Memory)
	r.Used.CPU = addResourceQuantity(r.Used.CPU, other.Used.CPU)
	r.Used.Memory = addResourceQuantity(r.Used.Memory, other.Used.Memory)

	// Add XPU resources
	r.Requests.XPU = mergeXPUResources(r.Requests.XPU, other.Requests.XPU)
	r.Limits.XPU = mergeXPUResources(r.Limits.XPU, other.Limits.XPU)
	r.Used.XPU = mergeXPUResources(r.Used.XPU, other.Used.XPU)
}

func mergeXPUResources(existing, other []*XPUResource) []*XPUResource {
	if len(other) == 0 {
		return existing
	}

	// Create a map to store merged XPU resources
	xpuMap := make(map[string]*XPUResource)

	// Add existing XPU resources to map
	for _, xpu := range existing {
		xpuMap[xpu.Name] = &XPUResource{
			Name:   xpu.Name,
			Count:  xpu.Count,
			Cores:  xpu.Cores,
			Memory: xpu.Memory,
		}
	}

	// Merge other XPU resources
	for _, xpu := range other {
		if existingXPU, ok := xpuMap[xpu.Name]; ok {
			// Adxd resources for existing XPU type
			existingXPU.Count = existingXPU.Count + xpu.Count
			existingXPU.Cores = existingXPU.Cores + xpu.Cores
			existingXPU.Memory = addResourceQuantity(existingXPU.Memory, xpu.Memory)
		} else {
			// Add new XPU type
			xpuMap[xpu.Name] = &XPUResource{
				Name:   xpu.Name,
				Count:  xpu.Count,
				Cores:  xpu.Cores,
				Memory: xpu.Memory,
			}
		}
	}

	// Convert map back to slice
	result := make([]*XPUResource, 0, len(xpuMap))
	for _, xpu := range xpuMap {
		result = append(result, xpu)
	}

	// Sort result by XPU name for consistent ordering
	sort.Slice(result, func(i, j int) bool {
		return result[i].Name < result[j].Name
	})

	return result
}
func addResourceQuantity(a, b string) string {
	if a == "" {
		return b
	}
	if b == "" {
		return a
	}

	qa, err := resource.ParseQuantity(a)
	if err != nil {
		return a
	}

	qb, err := resource.ParseQuantity(b)
	if err != nil {
		return a
	}

	qa.Add(qb)
	return qa.String()
}

type TaskType string

const (
	TaskTypeModelTraining     TaskType = "MODEL_TRAINING"
	TaskTypeModelQuantization TaskType = "MODEL_QUANTIZATION"
	TaskTypeModelEvaluation   TaskType = "MODEL_EVALUATION"
	TaskTypeModelDownload     TaskType = "MODEL_DOWNLOAD"
	TaskTypeCorupsProcessing  TaskType = "CORUPS_PROCESSING"
	TaskTypePipeline          TaskType = "PIPELINE"
	TaskTypeUnknown           TaskType = "UNKNOWN"

	TaskTypeKnowledgeProcessing TaskType = "KNOWLEDGE_PROCESSING"

	// TaskTypeModelFineTuning  TaskType = "MODEL_FINE_TUNING"
	// TaskTypeCorupsEvaluation TaskType = "CORUPS_EVALUATION"
)

func ToTaskType(t string) TaskType {
	switch strings.ToUpper(t) {
	case string(TaskTypeModelTraining):
		return TaskTypeModelTraining
	case string(TaskTypeModelQuantization):
		return TaskTypeModelQuantization
	case string(TaskTypeModelEvaluation):
		return TaskTypeModelEvaluation
	case string(TaskTypeModelDownload):
		return TaskTypeModelDownload
	case string(TaskTypeCorupsProcessing):
		return TaskTypeCorupsProcessing
	case string(TaskTypePipeline):
		return TaskTypePipeline
	case string(TaskTypeKnowledgeProcessing):
		return TaskTypeKnowledgeProcessing
	default:
		return TaskTypeUnknown
	}
}

type TaskPriority string

const (
	TaskPriorityDefault TaskPriority = "0"

	TaskPriorityLow    TaskPriority = "10"
	TaskPriorityMedium TaskPriority = "100"
	TaskPriorityHigh   TaskPriority = "1000"
)

func (tp *TaskPriority) ToInt() int {
	switch *tp {
	case TaskPriorityHigh:
		return 1000
	case TaskPriorityMedium:
		return 100
	case TaskPriorityLow:
		return 10
	default:
		return 0
	}
}

func ToTaskPriority(p *int32) TaskPriority {
	if p == nil {
		return TaskPriorityDefault
	} else if *p < 50 {
		return TaskPriorityLow
	} else if *p < 500 {
		return TaskPriorityMedium
	}
	return TaskPriorityHigh
}

type TaskStatus string

const (
	TaskStatusPending  TaskStatus = "pending"
	TaskStatusAdmitted TaskStatus = "admitted"
	TaskStatusRunning  TaskStatus = "running"
	// StatusPaused    TaskStatus = "paused"
	TaskStatusSucceeded TaskStatus = "succeeded"
	TaskStatusFailed    TaskStatus = "failed"
	// StatusRetrying  TaskStatus = "retrying"
	TaskStatusCancelled TaskStatus = "cancelled"
	TaskStatusUnknown   TaskStatus = "unknown"
	// TaskStatusTimeout   TaskStatus = "timeout"

	// TaskStatusSuspended TaskStatus = "suspended"
)

// subTask == kueue workload
type SubTask struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	WorkloadRef *kueuev1beta1.Workload `json:"-"`
	Status      SubTaskStatus          `json:"status"`
	CreatedAt   time.Time              `json:"created_at"`
	StartedAt   *time.Time             `json:"started_at"`
	EndedAt     *time.Time             `json:"ended_at"`

	Resources *Resources `json:"resources"`

	Pods []*Pod `json:"pods"`
}

func (st *SubTask) RunningNodeNames() []string {
	nodeNames := make([]string, 0)
	for _, pod := range st.Pods {
		if pod.NodeName != "" {
			nodeNames = append(nodeNames, pod.NodeName)
		}
	}
	return nodeNames
}

type WorkloadRef struct {
	Name      string `json:"name"`
	Namespace string `json:"namespace"`
	UID       string `json:"uid"`
}

type Pod struct {
	Name      string          `json:"name"`
	Namespace string          `json:"namespace"`
	NodeName  string          `json:"node_name"`
	Phase     corev1.PodPhase `json:"phase"`
	HostIP    string          `json:"host_ip"`
	PodIP     string          `json:"-"`
	StartedAt *time.Time      `json:"started_at"`
	EndedAt   *time.Time      `json:"ended_at"`

	ScheduledAt *time.Time `json:"scheduled_at"`

	Resources *Resources `json:"resources"`

	// Events []*PodEvent `json:"events"`
	// Logs   []*PodLog   `json:"logs"`

	// Node *Node `json:"node"`
	Containers []*Container `json:"containers"`
}

type Container struct {
	Name      string     `json:"name"`
	Image     string     `json:"image"`
	Status    string     `json:"status"`
	Resources *Resources `json:"resources"`
}

type PodEvent struct {
	Reason    string    `json:"reason"`
	Message   string    `json:"message"`
	Type      string    `json:"type"`
	Source    string    `json:"source"`
	FirstSeen time.Time `json:"first_seen"`
	LastSeen  time.Time `json:"last_seen"`
	Count     int       `json:"count"`
}

type PodLog struct {
	Timestamp time.Time `json:"timestamp"`
	Message   string    `json:"message"`
	Level     string    `json:"level"`
}

type SubTaskStatus string

const (
	SubTaskAdmitted           = "Admitted"
	SubTaskQuotaReserved      = "QuotaReserved"
	SubTaskFinished           = "Finished"
	SubTaskPodsReady          = "PodsReady"
	SubTaskEvicted            = "Evicted"
	SubTaskPreempted          = "Preempted"
	SubTaskRequeued           = "Requeued"
	SubTaskDeactivationTarget = "DeactivationTarget"
)

type Node struct {
	ID     string
	Name   string
	IP     string
	Status string
	Labels map[string]string
}

func (t *Task) UpdateTaskStatus() *Task {
	if len(t.SubTasks) == 0 {
		t.Status = TaskStatusUnknown
		return t
	}

	allQuotaReserved := true
	hasAdmitted := false
	allPodsSucceeded := true
	anyPodFailed := false
	totalPods := 0
	succeededPods := 0

	for _, subTask := range t.SubTasks {
		switch subTask.Status {
		case SubTaskAdmitted:
			hasAdmitted = true
			allQuotaReserved = false
		case SubTaskQuotaReserved:
		default:
			allQuotaReserved = false
		}

		for _, pod := range subTask.Pods {
			totalPods++
			switch pod.Phase {
			case corev1.PodSucceeded:
				succeededPods++
			case corev1.PodFailed:
				anyPodFailed = true
				allPodsSucceeded = false
			default:
				allPodsSucceeded = false
			}
		}
	}

	// Apply status logic according to requirements:
	// 1. All workloads QuotaReserved -> pending
	// 2. Any workload Admitted -> running
	// 3. Any pod Failed -> failed
	// 4. All pods Succeeded -> succeeded

	if anyPodFailed {
		t.Status = TaskStatusFailed
	} else if totalPods > 0 && allPodsSucceeded && succeededPods == totalPods {
		t.Status = TaskStatusSucceeded
	} else if hasAdmitted {
		t.Status = TaskStatusRunning
	} else if allQuotaReserved {
		t.Status = TaskStatusPending
	}

	return t
}

func (t *Task) UpdateTaskStartedAt() *Task {
	for _, st := range t.SubTasks {
		if st.StartedAt != nil {
			if t.StartedAt == nil || st.StartedAt.Before(*t.StartedAt) {
				t.StartedAt = st.StartedAt
			}
		}
	}
	return t
}

func (t *Task) UpdateTaskEndedAt() *Task {
	for _, st := range t.SubTasks {
		if st.EndedAt != nil {
			if t.EndedAt == nil || st.EndedAt.After(*t.EndedAt) {
				t.EndedAt = st.EndedAt
			}
		} else {
			t.EndedAt = nil
			break
		}
	}
	return t
}

func (t *Task) UpdateQueueDuration() *Task {
	switch t.Status {
	case TaskStatusPending:
		// if time before createdAt, set to 0
		t.QueuedDuration = time.Since(t.CreatedAt)
		if t.QueuedDuration < 0 {
			t.QueuedDuration = 0
		}
	case TaskStatusAdmitted, TaskStatusRunning, TaskStatusSucceeded, TaskStatusFailed, TaskStatusCancelled:
		if t.StartedAt != nil {
			// fix queued time < 0
			if t.StartedAt.Before(t.CreatedAt) {
				t.QueuedDuration = 0
			} else {
				t.QueuedDuration = t.StartedAt.Sub(t.CreatedAt)
			}
		}
	}
	return t
}
