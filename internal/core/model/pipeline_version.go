package model

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/argoproj/argo-workflows/v3/pkg/apis/workflow/v1alpha1"
	"sigs.k8s.io/yaml"
	"strings"
	"transwarp.io/applied-ai/aiot/vision-std/boot/k8s"
	"transwarp.io/mlops/pipeline/internal/core/consts"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
	"unicode"

	pb "transwarp.io/aip/llmops-common/pb/pipeline"
	"transwarp.io/mlops/mlops-std/util"
	"transwarp.io/mlops/pipeline/internal/dao/model"
)

type PipelineVersion struct {
	Id              string
	PipelineId      string
	Name            string
	TaskFlow        *TaskFlow
	CreateTime      int64
	UpdateTime      int64
	Desc            string
	SchedulingStyle pb.PipelineVersion_SchedulingStyle
	// cron
	TimingConfig *TimingConfig
	// event
	EventConfig *pb.EventConfig

	WorkflowAdvancedConfig *WorkflowAdvancedConfig
}

func (version *PipelineVersion) FromPb(pb *pb.PipelineVersion) *PipelineVersion {
	version.Id = pb.Id
	version.PipelineId = pb.PipelineId
	version.Name = pb.Name
	version.TaskFlow = (&TaskFlow{}).FromPb(pb.TaskFlow)
	version.CreateTime = pb.CreateTime
	version.UpdateTime = pb.UpdateTime
	version.Desc = pb.Desc
	version.SchedulingStyle = pb.Style
	version.TimingConfig = (&TimingConfig{}).FromPb(pb.TimingConfig)
	version.WorkflowAdvancedConfig = nil
	version.EventConfig = pb.EventConfig
	return version
}

func (version *PipelineVersion) FromPbV2(p *pb.PipelineVersionV2) *PipelineVersion {
	version.Id = p.GetId()
	version.PipelineId = p.GetPipelineId()
	version.Name = p.GetName()
	version.TaskFlow = (&TaskFlow{}).FromPbV2(p.GetTaskFlow())
	version.CreateTime = p.GetCreateTime()
	version.UpdateTime = p.GetUpdateTime()
	version.Desc = p.GetDesc()
	version.SchedulingStyle = pb.PipelineVersion_SchedulingStyle(p.GetStyle())
	version.TimingConfig = (&TimingConfig{}).FromPb(p.GetTimingConfig())
	version.WorkflowAdvancedConfig = nil
	version.EventConfig = p.EventConfig
	return version
}

func (version *PipelineVersion) ToPb() *pb.PipelineVersion {
	return &pb.PipelineVersion{
		Id:           version.Id,
		PipelineId:   version.PipelineId,
		Name:         version.Name,
		TaskFlow:     version.TaskFlow.ToPb(),
		CreateTime:   version.CreateTime,
		UpdateTime:   version.UpdateTime,
		Desc:         version.Desc,
		Style:        version.SchedulingStyle,
		TimingConfig: version.TimingConfig.ToPb(),
		EventConfig:  version.EventConfig,
	}
}

func (version *PipelineVersion) ToPbV2() *pb.PipelineVersionV2 {
	return &pb.PipelineVersionV2{
		Id:           version.Id,
		PipelineId:   version.PipelineId,
		Name:         version.Name,
		TaskFlow:     version.TaskFlow.ToPbV2(),
		CreateTime:   version.CreateTime,
		UpdateTime:   version.UpdateTime,
		Desc:         version.Desc,
		Style:        pb.PipelineVersionV2_SchedulingStyle(version.SchedulingStyle),
		TimingConfig: version.TimingConfig.ToPb(),
		EventConfig:  version.EventConfig,
	}
}

func (version *PipelineVersion) FromModel(model *model.PipelineVersion) *PipelineVersion {
	version.Id = model.ID
	version.PipelineId = model.PipelineID
	version.Name = model.Name
	version.TaskFlow = &TaskFlow{}
	util.FromJson(model.TaskFlow, version.TaskFlow)
	version.CreateTime = model.CreateTime.Unix()
	version.UpdateTime = model.UpdateTime.Unix()
	version.Desc = model.Desc
	version.SchedulingStyle = pb.PipelineVersion_SchedulingStyle(model.SchedulingStyle)
	version.TimingConfig = &TimingConfig{
		Cron:         model.TimingConfigCron,
		ScheduleType: pb.TimingConfig_ScheduleType(model.TimingConfigScheduleType),
	}
	version.WorkflowAdvancedConfig = nil

	if version.SchedulingStyle == pb.PipelineVersion_EVENT {
		version.EventConfig = &pb.EventConfig{
			Type: pb.EventType(model.EventType),
		}
		switch version.EventConfig.Type {
		case pb.EventType_FileAssets:
			version.EventConfig.FileAssetsParam = &pb.FileAssetsParam{}
			util.FromJson(model.EventParams, version.EventConfig.FileAssetsParam)
		}
	}

	return version
}

func normalizeId(s string) string {
	if s == "" {
		return s
	}
	// 如果第一个 rune 是数字，就在前面加个 n
	if unicode.IsDigit([]rune(s)[0]) {
		return "n" + s
	}
	return s
}
func normalizeIds(ss []string) []string {
	for index := range ss {
		ss[index] = normalizeId(ss[index])
	}
	return ss
}

func (version *PipelineVersion) ToModel() *model.PipelineVersion {
	if version.TaskFlow != nil {
		for index, node := range version.TaskFlow.Nodes {
			version.TaskFlow.Nodes[index].Id = normalizeId(node.Id)
			if node.Inputs != nil {
				for inputIndex, input := range node.Inputs {
					if input.From != nil {
						version.TaskFlow.Nodes[index].Inputs[inputIndex].From.NodeId = normalizeId(input.From.NodeId)
					}
				}
			}
		}
		for index := range version.TaskFlow.Dependencies {
			version.TaskFlow.Dependencies[index].NodeId = normalizeId(version.TaskFlow.Dependencies[index].NodeId)
			version.TaskFlow.Dependencies[index].DepNodeId = normalizeIds(version.TaskFlow.Dependencies[index].DepNodeId)
		}
	}
	pipelineVersion := &model.PipelineVersion{
		ID:              version.Id,
		PipelineID:      version.PipelineId,
		Name:            version.Name,
		TaskFlow:        util.ToJson(version.TaskFlow),
		Desc:            version.Desc,
		SchedulingStyle: int32(version.SchedulingStyle),
	}
	if version.TimingConfig != nil {
		pipelineVersion.TimingConfigCron = version.TimingConfig.Cron
		pipelineVersion.TimingConfigScheduleType = int32(version.TimingConfig.ScheduleType)
	}
	if version.EventConfig != nil {
		pipelineVersion.EventType = int32(version.EventConfig.Type)
		switch version.EventConfig.Type {
		case pb.EventType_FileAssets:
			pipelineVersion.EventParams = util.ToJson(version.EventConfig.FileAssetsParam)
		}

	}
	return pipelineVersion
}

func (version *PipelineVersion) ToWorkflow(ctx context.Context, pipeline *Pipeline, tenantUid string) (*v1alpha1.Workflow, error) {

	labels := version.ToLabels(pipeline.ProjectId)

	annotations, err := version.ToAnnotations(pipeline.CreateUser, pipeline.ProjectId, tenantUid)
	if err != nil {
		return nil, err
	}

	workflow, err := version.TaskFlow.ToWorkflow(ctx, strings.ReplaceAll(version.Id, "/", ""), pipeline.ProjectId, labels, annotations)
	if err != nil {
		return nil, err
	}
	if version.SchedulingStyle == pb.PipelineVersion_EVENT && version.EventConfig != nil {
		switch version.EventConfig.Type {
		case pb.EventType_FileAssets:
			if workflow.Spec.Arguments.Parameters == nil {
				workflow.Spec.Arguments.Parameters = make([]v1alpha1.Parameter, 0, 1)
			}
			workflow.Spec.Arguments.Parameters = append([]v1alpha1.Parameter{{Name: "asset_id"}, {Name: "asset_version_id"}}, workflow.Spec.Arguments.Parameters...)
		}
	}

	return workflow, nil
}

func (version *PipelineVersion) ToYaml(ctx context.Context, pipeline *Pipeline, tenantUid string) ([]byte, error) {
	workflow, err := version.ToWorkflow(ctx, pipeline, tenantUid)
	if err != nil {
		return nil, err
	}
	json, _ := json.Marshal(workflow)
	yaml, _ := yaml.JSONToYAML(json)
	return yaml, nil
}

func (version *PipelineVersion) FromYaml(b []byte) (*PipelineVersion, error) {
	workflow := &v1alpha1.Workflow{}

	err := yaml.Unmarshal(b, workflow)
	if err != nil {
		zlog.SugarDebugf("from yaml err: %+v", err)
	}
	*version = PipelineVersion{}
	version.FromWorkflow(workflow)
	if err != nil {
		return nil, err
	}
	return version, nil
}

func (version *PipelineVersion) FromWorkflow(workflow *v1alpha1.Workflow) (*PipelineVersion, error) {
	name := workflow.Annotations[META_TASK_SOURCE_NAME]
	if name == "" {
		name = workflow.Name
	}
	*version = PipelineVersion{
		Name:     workflow.Annotations[META_TASK_SOURCE_NAME],
		TaskFlow: &TaskFlow{},
	}
	_, err := version.TaskFlow.FromWorkflow(workflow)
	if err != nil {
		return nil, err
	}
	return version, nil
}

func (version *PipelineVersion) ToLabels(projectId string) map[string]string {
	labels := map[string]string{
		consts.LabelLLMOpsProductKey:         consts.LabelLLMOpsProductValue,
		consts.LabelLLMOpsComponentKey:       consts.LabelLLMOpsComponentValue,
		consts.LabelLLMOpsManagedByKey:       k8s.CurrentNamespaceInCluster(),
		consts.LabelProjectId:                projectId,
		consts.LabelTaskRefSourceIDKey:       version.Id,
		consts.LabelTaskRefSourceTypeKey:     pb.TaskSourceType_PIPELINE.String(),
		consts.LabelTaskManagedByKey:         k8s.CurrentNamespaceInCluster(),
		consts.LabelKueueQueueNameKey:        consts.LabelKueueQueueNameValue,
		consts.LabelKueueWorkloadPriorityKey: fmt.Sprintf("%s-%s", k8s.CurrentNamespaceInCluster(), consts.LabelKueueWorkloadPriorityMedium),
	}
	return labels
}

func (version *PipelineVersion) ToAnnotations(creator, projectId, tenantUID string) (map[string]string, error) {
	annotations := map[string]string{
		consts.AnnotationTaskRefSourceNameKey: version.Name,
		consts.AnnotationTaskRefSourceIDKey:   version.Id,
		consts.AnnotationTaskNameKey:          version.Name,
		consts.AnnotationTaskTypeKey:          pb.TaskSourceType_PIPELINE.String(),
		consts.AnnotationTaskDescriptionKey:   version.Desc,
		consts.AnnotationTaskCreatorKey:       creator,
		consts.AnnotationTaskTenantIDKey:      tenantUID,
		consts.AnnotationTaskProjectIDKey:     projectId,
	}
	return annotations, nil
}
