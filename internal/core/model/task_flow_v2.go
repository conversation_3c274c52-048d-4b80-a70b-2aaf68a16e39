package model

import (
	"context"
	"fmt"
	"k8s.io/utils/ptr"
	pb "transwarp.io/aip/llmops-common/pb/serving"

	"github.com/argoproj/argo-workflows/v3/pkg/apis/workflow/v1alpha1"
	"github.com/spf13/cast"
	apiv1 "k8s.io/api/core/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/utils/pointer"
	"transwarp.io/aip/llmops-common/pkg/serving"
	"transwarp.io/applied-ai/aiot/vision-std/stdmetric"
	k8s_util "transwarp.io/mlops/mlops-std/k8s"
	"transwarp.io/mlops/mlops-std/stderr"
	"transwarp.io/mlops/mlops-std/util"
)

func (taskFlow *TaskFlow) toWorkflowV2(ctx context.Context, name, proid string, labels map[string]string, annotations map[string]string) (*v1alpha1.Workflow, error) {
	depsMap := map[string][]string{} // node -> deps
	for _, dep := range taskFlow.Dependencies {
		depsMap[dep.GetNodeId()] = dep.GetDepNodeId()
	}

	// 提前生成多实例依赖关系
	depsRevsMap := map[string][]string{} // dep -> nodes, 多实例时获取依赖关系
	for _, dep := range taskFlow.Dependencies {
		for _, d := range dep.GetDepNodeId() {
			depsRevsMap[d] = append(depsRevsMap[d], dep.GetNodeId())
		}
	}
	for _, node := range taskFlow.Nodes {
		if node.UnifyResource.GetDistributed().GetEnabled() && node.UnifyResource.GetDistributed().GetNumWorkers() > 1 {
			for _, nid := range depsRevsMap[node.Id] {
				for i := int32(0); i < node.UnifyResource.GetDistributed().GetNumWorkers(); i++ {
					depsMap[nid] = append(depsMap[nid], fmt.Sprintf("%s-%d", node.Id, i))
				}
			}
		}
	}

	templates := make([]v1alpha1.Template, 0, len(taskFlow.Nodes)+1)
	tasks := make([]v1alpha1.DAGTask, 0, len(taskFlow.Nodes))
	var dagEventParams []v1alpha1.Parameter

	for _, node := range taskFlow.Nodes {
		node.conformComponent()
		inputs := v1alpha1.Artifacts{}
		outputs := v1alpha1.Artifacts{}
		dagFroms := v1alpha1.Artifacts{}
		var parameters []v1alpha1.Parameter
		var eventParams []v1alpha1.Parameter
		for _, input := range node.Inputs {
			if input.GetFrom() == nil {
				return nil, stderr.PipelineInputFromIsNil.Error()
			}
			inputs = append(inputs, v1alpha1.Artifact{
				Name: input.GetName(),
				Path: input.GetPath(),
			})
			dagFroms = append(dagFroms, v1alpha1.Artifact{
				Name: input.GetName(),
				From: fmt.Sprintf("{{tasks.%s.outputs.artifacts.%s}}", input.GetFrom().GetNodeId(), input.GetFrom().GetArtifactName()),
			})
		}
		for _, output := range node.Outputs {
			outputs = append(outputs, v1alpha1.Artifact{
				Name: output.GetName(),
				Path: output.GetPath(),
			})
		}
		for key, _ := range node.EventParams {
			parameter := v1alpha1.Parameter{
				Name: key,
			}
			parameterFrom := v1alpha1.Parameter{
				Name:  key,
				Value: v1alpha1.AnyStringPtr(fmt.Sprintf("{{inputs.parameters.%s}}", key)),
			}
			parameters = append(parameters, parameter)
			eventParams = append(eventParams, parameterFrom)
			dagEventParams = append(dagEventParams, parameter)
		}
		for key, _ := range node.EventParams {
			parameter := v1alpha1.Parameter{
				Name: key,
			}
			parameters = append(parameters, parameter)
		}
		for key, value := range node.Params {
			pValue := value
			parameters = append(parameters, v1alpha1.Parameter{
				Name:  key,
				Value: v1alpha1.AnyStringPtr(pValue),
			})
		}
		task := v1alpha1.DAGTask{
			Name:         node.Id,
			Template:     node.Id,
			Dependencies: depsMap[node.Id],
			Arguments: v1alpha1.Arguments{
				Artifacts:  dagFroms,
				Parameters: eventParams,
			},
		}
		mainContainer, err := node.Container.ToK8s(ctx)
		if err != nil {
			return nil, err
		}
		mainContainer.VolumeMounts = append(mainContainer.VolumeMounts, apiv1.VolumeMount{
			Name:      PodInfoVolumeName,
			MountPath: AnnotationsPath,
		})
		mainContainer.Env = append(mainContainer.Env,
			apiv1.EnvVar{
				Name:  EnvGatewayUrl,
				Value: fmt.Sprintf("http://autocv-gateway-service.%s.svc:80", k8s_util.CurrentNamespaceInCluster()),
			}, apiv1.EnvVar{
				Name:      EnvPodUID,
				ValueFrom: &apiv1.EnvVarSource{FieldRef: &apiv1.ObjectFieldSelector{FieldPath: "metadata.uid"}},
			}, apiv1.EnvVar{
				Name:      EnvPodName,
				ValueFrom: &apiv1.EnvVarSource{FieldRef: &apiv1.ObjectFieldSelector{FieldPath: "metadata.name"}},
			},
		)
		arch := node.UnifyResource.GetArch()
		servingRes := &serving.PodResourceDTO{
			NodeChooseCfg:  node.UnifyResource.GetNodeChooseCfg(),
			ResourceRuleId: cast.ToInt32(node.UnifyResource.GetResourceRuleId()),
			ResourceRule:   node.UnifyResource.GetResourceRule(),
			Resource:       node.UnifyResource.GetResource(),
			Arch:           arch,
			Distributed:    node.UnifyResource.GetDistributed(),
			IsAdvancedMode: node.UnifyResource.GetIsAdvancedMode(),
		}
		if servingRes.ResourceRuleId == 0 {
			servingRes.IsAdvancedMode = true
			if servingRes.Resource == nil {
				servingRes.Resource = &pb.Resource{ // TODO: config
					CpuLimit:      "1",
					CpuRequest:    "200m",
					MemoryLimit:   "1Gi",
					MemoryRequest: "256Mi",
				}
			}
		}
		spec, err := servingRes.ToPodResourceDO(ctx)
		if err != nil {
			return nil, stderr.Internal.Errorf("ToPodResourceDO: %v", err)
		}
		mainContainer.Resources = *spec.Container.Resources
		nodeLabels := node.Labels

		nodeLabels, _, err = stdmetric.WithMonitorKeys(nodeLabels, nil, stdmetric.ResourceTypeTask, node.Id, name, proid)
		if err != nil {
			return nil, err
		}
		for k, v := range spec.Labels {
			nodeLabels[k] = v
		}
		for k, v := range labels {
			nodeLabels[k] = v
		}
		nodeAnnotations := map[string]string{
			MetaNodeId:             node.Id,
			MetaNodeName:           node.Name,
			MetaOwner:              util.GetUsername(ctx),
			MetaTemplateType:       node.TemplateSource.GetTemplateType().String(),
			MetaTemplateSourceId:   node.TemplateSource.GetSourceId(),
			MetaTemplateSourceName: node.TemplateSource.GetSourceName(),
			MetaTemplateSourceInfo: node.TemplateSource.GetSourceInfo(),
			MetaGPUUseConf:         util.ToJson(node.Container.Resources),
		}

		for k, v := range node.Annotations {
			nodeAnnotations[k] = v
		}
		for k, v := range spec.Annotations {
			nodeAnnotations[k] = v
		}
		for k, v := range annotations {
			nodeAnnotations[k] = v
		}

		volumes, err := node.VolumeCfgs.ToK8s()
		if err != nil {
			return nil, err
		}
		volumes = append(volumes, apiv1.Volume{
			Name: PodInfoVolumeName,
			VolumeSource: apiv1.VolumeSource{
				DownwardAPI: &apiv1.DownwardAPIVolumeSource{
					Items: []apiv1.DownwardAPIVolumeFile{
						{
							Mode: pointer.Int32Ptr(420),
							FieldRef: &apiv1.ObjectFieldSelector{
								APIVersion: "v1",
								FieldPath:  "metadata.annotations",
							},
							Path: "annotations",
						},
					},
				},
			},
		})

		// K8s API 模式的 executor
		mainContainer.VolumeMounts = append(mainContainer.VolumeMounts, apiv1.VolumeMount{
			Name:      ArtifactsVolumeName,
			MountPath: ArtifactsPath,
		})
		volumes = append(volumes, apiv1.Volume{
			Name: ArtifactsVolumeName,
			VolumeSource: apiv1.VolumeSource{
				EmptyDir: &apiv1.EmptyDirVolumeSource{},
			},
		})
		selector := node.NodeSelector
		if selector == nil {
			selector = make(map[string]string)
		}
		label, err := serving.LabelSelectorToStr(ctx)
		if err == nil && len(label) != 0 {
			selector[label] = "true"
		}
		template := v1alpha1.Template{
			Name: node.Id,
			Inputs: v1alpha1.Inputs{
				Artifacts:  inputs,
				Parameters: parameters,
			},
			Outputs: v1alpha1.Outputs{
				Artifacts: outputs,
			},
			Container: mainContainer,
			Metadata: v1alpha1.Metadata{
				Labels:      nodeLabels,
				Annotations: nodeAnnotations,
			},
			NodeSelector: selector,
			Affinity:     spec.Affinity,
			Volumes:      volumes,
		}
		if node.AdvancedConfig != nil {
			template.ServiceAccountName = node.AdvancedConfig.ServiceAccountName
		}
		templates = append(templates, template)
		tasks = append(tasks, task)
		if node.UnifyResource.GetDistributed().GetEnabled() && node.UnifyResource.GetDistributed().GetNumWorkers() > 1 {
			for i := int32(0); i < node.UnifyResource.GetDistributed().GetNumWorkers(); i++ {
				t := task.DeepCopy()
				t.Name = fmt.Sprintf("%s-%d", t.Name, i)
				tasks = append(tasks, *t)
			}
		}
	}
	templates = append(templates, v1alpha1.Template{
		Name: name,
		DAG: &v1alpha1.DAGTemplate{
			Tasks: tasks,
		},
	})
	workflow := &v1alpha1.Workflow{
		TypeMeta: v1.TypeMeta{
			Kind:       "Workflow",
			APIVersion: "argoproj.io/v1alpha1",
		},
		ObjectMeta: v1.ObjectMeta{
			GenerateName: name,
			Annotations:  annotations,
			Labels:       labels,
		},
		Spec: v1alpha1.WorkflowSpec{
			Entrypoint:         name,
			Templates:          templates,
			ServiceAccountName: PipelineServiceAccount,
			HostNetwork:        &taskFlow.HostNetwork,
			TTLStrategy: &v1alpha1.TTLStrategy{
				SecondsAfterSuccess: ptr.To(int32(60 * 60)),      // TODO: Config
				SecondsAfterFailure: ptr.To(int32(24 * 60 * 60)), // TODO: Config
			},
		},
	}

	workflow.Annotations[MetaTaskFlow] = util.ToJson(taskFlow)
	return workflow, nil
}
