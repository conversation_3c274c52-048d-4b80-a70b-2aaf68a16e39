package model

import (
	apiv1 "k8s.io/api/core/v1"
	"strings"
	"transwarp.io/aip/llmops-common/pb/common"
	"transwarp.io/mlops/mlops-std/stderr"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

type MountPaths []*common.MountPath

const SFSPathPrefix = "sfs://"

func GetSubPathFromSFS(sfsPath string) (string, error) {

	if strings.HasPrefix(sfsPath, SFSPathPrefix) {
		sfsPath = sfsPath[len(SFSPathPrefix):]
	}

	if strings.HasPrefix(sfsPath, "/") {
		sfsPath = sfsPath[1:]
	}
	// tenants/{tenant_id}/projs/{proj_id}
	if strings.HasPrefix(sfsPath, "tenants") {
		paths := strings.Split(sfsPath, "/")
		if len(paths) >= 2 {
			if len(paths) == 2 {
				return "", nil
			} else {
				return strings.Join(paths[2:], "/"), nil
			}
		} else {
			return "", stderr.MLOpsServiceModelTransferErr.Error("invalid sfs path :%v", sfsPath)
		}
	}
	return sfsPath, nil
}

func (mountPaths MountPaths) ToK8s() []apiv1.VolumeMount {
	var mounts []apiv1.VolumeMount
	for _, mountPath := range mountPaths {
		subPath, err := GetSubPathFromSFS(mountPath.SubPath)
		if err != nil {
			zlog.SugarErrorf("GetSubPathFromSFS err: %+v", err)
			continue
		}
		mounts = append(mounts, apiv1.VolumeMount{
			Name:      mountPath.VolumeName,
			MountPath: mountPath.MountPath,
			SubPath:   subPath,
			ReadOnly:  mountPath.ReadOnly,
		})
	}
	return mounts
}

func (mountPaths *MountPaths) FromK8s(volumeMounts []apiv1.VolumeMount) *MountPaths {
	*mountPaths = []*common.MountPath{}

	for _, volumeMount := range volumeMounts {
		*mountPaths = append(*mountPaths, &common.MountPath{
			VolumeName: volumeMount.Name,
			MountPath:  volumeMount.MountPath,
			SubPath:    volumeMount.SubPath,
			ReadOnly:   volumeMount.ReadOnly,
		})
	}
	return mountPaths
}
