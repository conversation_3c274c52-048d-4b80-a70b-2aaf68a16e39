package util

import (
	"os"
	"path/filepath"
	"strings"
	"sync"

	"k8s.io/client-go/discovery"
	"transwarp.io/applied-ai/aiot/vision-std/boot/k8s"
	"transwarp.io/mlops/pipeline/internal/core/consts"

	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

var currentNamespace = sync.OnceValues(func() (string, error) {
	if e := os.Getenv("CURRENT_NAMESPACE"); e != "" {
		return e, nil
	}
	if e := os.Getenv("POD_NAMESPACE"); e != "" {
		return e, nil
	}
	nsFile := "/run/secrets/kubernetes.io/serviceaccount/namespace"
	ns, err := os.ReadFile(nsFile)
	if err != nil {
		zlog.SugarErrorf("Get ns from file %s failed: %v", nsFile, err)
		return "", err
	}
	return strings.TrimSuffix(string(ns), "\n"), err
})

var currentPodName = sync.OnceValues(func() (string, error) {
	if name := os.Getenv("POD_NAME"); name != "" {
		return name, nil
	} else if name, err := os.Hostname(); err == nil && name != "" {
		return name, nil
	}
	return "", nil
})

func GetKubeConfig() (*rest.Config, error) {
	// os.Setenv("LOCAL_DEBUG_MODEL", "TRUE")
	// os.Setenv("MLOPS_CONF_DIR", "/home/<USER>/workspace/sophon/go/transwarp.io/aip/mlops-pipeline/conf")
	if strings.ToUpper(os.Getenv("LOCAL_DEBUG_MODEL")) == "TRUE" {
		kubeConfigPath := filepath.Join(os.Getenv("MLOPS_CONF_DIR"), "kubeconfig")
		return clientcmd.BuildConfigFromFlags("", kubeConfigPath)
	}
	return rest.InClusterConfig()
}

func GetCurrentNamespace() string {
	ns, err := currentNamespace()
	if err != nil {
		zlog.SugarErrorf("Get current namespace failed: %v", err)
		// return default value
		return "llmops"
	}
	return ns
}

func GetCurrentPodName() string {
	name, err := currentPodName()
	if err != nil {
		zlog.SugarErrorf("Get current pod name failed: %v", err)
		return "pipeline"
	}
	return name
}

func InferGVRFromGVK(gvk schema.GroupVersionKind) schema.GroupVersionResource {
	resource := ""
	switch gvk.Kind {
	case "Pod":
		resource = "pods"
	case "Deployment":
		resource = "deployments"
	case "ReplicaSet":
		resource = "replicasets"
	case "StatefulSet":
		resource = "statefulsets"
	case "DaemonSet":
		resource = "daemonsets"
	case "Job":
		resource = "jobs"
	case "CronJob":
		resource = "cronjobs"
	case "Workload":
		resource = "workloads"
	case "JobSet":
		resource = "jobsets"
	case "LeaderWorkerSet":
		resource = "leaderworkersets"
	case "Workflow":
		resource = "workflows"
	case "CronWorkflow":
		resource = "cronworkflows"
	case "RayJob":
		resource = "rayjobs"
	case "RayCluster":
		resource = "rayclusters"
	case "AppWrapper":
		resource = "appwrappers"
	case "EventBus":
		resource = "eventbus"
	default:
		resource = strings.ToLower(gvk.Kind) + "s"
		zlog.SugarWarnf("Warning: Using heuristic for resource name %s for Kind '%s'. Actual resource name might differ.", resource, gvk.Kind)
	}

	return gvk.GroupVersion().WithResource(resource)
}

func CustomLabels() map[string]string {
	labels := map[string]string{
		consts.LabelLLMOpsProductKey:   consts.LabelLLMOpsProductValue,
		consts.LabelLLMOpsComponentKey: consts.LabelLLMOpsComponentValue,
		consts.LabelLLMOpsManagedByKey: k8s.CurrentNamespaceInCluster(),
		consts.LabelTaskManagedByKey:   k8s.CurrentNamespaceInCluster(),
	}
	return labels
}

func HasLLMOpsManagedLabel(labels map[string]string) bool {
	if v, ok := labels[consts.LabelLLMOpsManagedByKey]; ok && v == GetCurrentNamespace() {
		return true
	}

	return false
}

func SupportAPIResources(discoveryClient *discovery.DiscoveryClient, groupVersion string, kind string) bool {
	apiResources, err := discoveryClient.ServerResourcesForGroupVersion(groupVersion)
	if err != nil {
		zlog.SugarWarnf("the server could not find the requested resource, groupVersion: %s kind: %s", groupVersion, kind)
		return false
	}

	for _, res := range apiResources.APIResources {
		if res.Kind == kind {
			return true
		}
	}
	return false
}
