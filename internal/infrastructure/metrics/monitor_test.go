package metrics

import (
	"context"
	"testing"

	"transwarp.io/mlops/pipeline/internal/config"
)

func TestMonitor(t *testing.T) {
	if testing.Short() {
		t.<PERSON>("Skipping long-running integration test in short mode")
	}

	mCfg, err := config.LoadMetricsConfig("../../../conf")
	if err != nil {
		t.<PERSON><PERSON>("failed to load metrics config: %v", err)
		return
	}

	monitor, err := newMetricsMonitor(mCfg)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("failed to create monitor: %v", err)
		return
	}

	ctx := context.Background()
	// monitor.Start(ctx)
	usage, err := monitor.QueryPodResourceUsage(ctx, "assetswts", "pod name")
	if err != nil {
		t.Errorf("failed to get resource usage: %v", err)
		return
	}

	t.Logf("resource usage: %+v", usage)
}
