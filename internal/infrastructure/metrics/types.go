package metrics

import (
	"time"
)

type QueryRequest struct {
	MetricName string            `json:"metric_name"`
	Query      string            `json:"query"`
	StartTime  time.Time         `json:"start_time"`
	EndTime    time.Time         `json:"end_time"`
	Step       time.Duration     `json:"step"`
	Labels     map[string]string `json:"labels"`
	Timeout    time.Duration     `json:"timeout"`
}

type QueryResult struct {
	MetricName string        `json:"metric_name"`
	Data       []MetricPoint `json:"data"`
	Labels     Labels        `json:"labels"`
	Timestamp  time.Time     `json:"timestamp"`
	Status     string        `json:"status"`
	Error      string        `json:"error,omitempty"`
}

type MetricPoint struct {
	Timestamp time.Time `json:"timestamp"`
	Value     float64   `json:"value"`
	Labels    Labels    `json:"labels"`
}

type Labels map[string]string

type PodResourceUsage struct {
	PodName   string `json:"pod_name,omitempty"`
	Namespace string `json:"namespace,omitempty"`

	CPUUsage    float64 `json:"cpu_usage,omitempty"`
	MemoryUsage float64 `json:"memory_usage,omitempty"`

	DeviceType     string  `json:"device_type,omitempty"`
	XPUUsage       float64 `json:"xpu_usage,omitempty"`
	XPUMemoryUsage float64 `json:"xpu_memory_usage,omitempty"`

	Timestamp time.Time `json:"timestamp"`
}
