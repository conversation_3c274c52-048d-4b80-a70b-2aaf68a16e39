package metrics

import (
	"bytes"
	"fmt"
	"strings"
	"text/template"
	"time"

	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

type MetricQueryTmpl struct {
	Name string
	Tmpl *template.Template
	Raw  string
}

func NewQueryMetricTmpl(name string, query string) (*MetricQueryTmpl, error) {
	tmpl, err := template.New(string(name)).Parse(query)
	if err != nil {
		return nil, err
	}

	return &MetricQueryTmpl{
		Name: name,
		Tmpl: tmpl,
		Raw:  query,
	}, nil
}

func (qmt *MetricQueryTmpl) RenderTmpl(data any) (string, error) {
	if qmt.Tmpl == nil {
		if err := qmt.initTmpl(data); err != nil {
			return "", err
		}
	}
	var buf bytes.Buffer
	err := qmt.Tmpl.Execute(&buf, data)
	if err != nil {
		return "", err
	}
	return buf.String(), nil
}

func (qmt *MetricQueryTmpl) initTmpl(data any) error {
	if qmt.Raw == "" {
		return fmt.Errorf("template %s not found", qmt.Name)
	}
	tmpl, err := template.New(string(qmt.Name)).Parse(qmt.Raw)
	if err != nil {
		return err
	}
	qmt.Tmpl = tmpl
	return nil
}

type TmplManager struct {
	templates map[string]*MetricQueryTmpl
	funcMap   template.FuncMap
}

func NewTemplateManager() *TmplManager {
	tm := &TmplManager{
		templates: make(map[string]*MetricQueryTmpl),
		funcMap:   make(template.FuncMap),
	}

	tm.registerTemplateFunctions()

	return tm
}

func (tm *TmplManager) registerTemplateFunctions() {
	tm.funcMap["join"] = strings.Join
	tm.funcMap["contains"] = strings.Contains
	tm.funcMap["hasPrefix"] = strings.HasPrefix
	tm.funcMap["hasSuffix"] = strings.HasSuffix
	tm.funcMap["toLower"] = strings.ToLower
	tm.funcMap["toUpper"] = strings.ToUpper
	tm.funcMap["replace"] = strings.ReplaceAll
	tm.funcMap["split"] = strings.Split

	tm.funcMap["now"] = time.Now
	tm.funcMap["duration"] = func(d string) time.Duration {
		duration, _ := time.ParseDuration(d)
		return duration
	}

	tm.funcMap["labelSelector"] = tm.buildLabelSelector
	tm.funcMap["labelMatch"] = tm.buildLabelMatch
	tm.funcMap["labelRegex"] = tm.buildLabelRegex

	tm.funcMap["if"] = func(condition bool, trueVal, falseVal interface{}) interface{} {
		if condition {
			return trueVal
		}
		return falseVal
	}

	tm.funcMap["default"] = func(defaultVal, val interface{}) interface{} {
		if val == nil || val == "" {
			return defaultVal
		}
		return val
	}
}

func (tm *TmplManager) buildLabelSelector(labels map[string]string) string {
	if len(labels) == 0 {
		return ""
	}

	var selectors []string
	for key, value := range labels {
		if value == "" {
			continue
		}
		selectors = append(selectors, fmt.Sprintf(`%s="%s"`, key, value))
	}

	if len(selectors) == 0 {
		return ""
	}

	return "{" + strings.Join(selectors, ",") + "}"
}

func (tm *TmplManager) buildLabelMatch(key, value string) string {
	if key == "" || value == "" {
		return ""
	}
	return fmt.Sprintf(`%s="%s"`, key, value)
}

func (tm *TmplManager) buildLabelRegex(key, pattern string) string {
	if key == "" || pattern == "" {
		return ""
	}
	return fmt.Sprintf(`%s=~"%s"`, key, pattern)
}

func (tm *TmplManager) RegisterTemplate(name string, query string) error {
	tmpl, err := template.New(string(name)).Funcs(tm.funcMap).Parse(query)
	if err != nil {
		return fmt.Errorf("failed to parse template %s: %w", name, err)
	}

	tm.templates[name] = &MetricQueryTmpl{
		Name: name,
		Tmpl: tmpl,
		Raw:  query,
	}

	return nil
}

func (tm *TmplManager) GenQueryStr(name string, data interface{}) (string, error) {
	tmpl, exists := tm.templates[name]
	if !exists {
		return "", fmt.Errorf("template %s not found", name)
	}

	if query, err := tmpl.RenderTmpl(data); err != nil {
		return "", fmt.Errorf("failed to execute template %s: %w", name, err)
	} else {
		zlog.SugarDebugf("Template %s executed: %s", name, query)
		return query, nil
	}
}

type QueryParams struct {
	Namespace string            `json:"namespace"`
	PodName   string            `json:"pod_name"`
	NodeName  string            `json:"node_name"`
	Container string            `json:"container"`
	Labels    map[string]string `json:"labels"`

	TimeRange string `json:"time_range"` // "5m", "1h"
	StartTime string `json:"start_time"`
	EndTime   string `json:"end_time"`

	Custom map[string]interface{} `json:"custom"`
}

func NewTemplateData() *QueryParams {
	return &QueryParams{
		Labels: make(map[string]string),
		Custom: make(map[string]interface{}),
	}
}

func (td *QueryParams) SetLabel(key, value string) *QueryParams {
	if td.Labels == nil {
		td.Labels = make(map[string]string)
	}
	td.Labels[key] = value
	return td
}

func (td *QueryParams) SetCustom(key string, value interface{}) *QueryParams {
	if td.Custom == nil {
		td.Custom = make(map[string]interface{})
	}
	td.Custom[key] = value
	return td
}
