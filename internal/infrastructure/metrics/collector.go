package metrics

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"sync"
	"time"

	config "transwarp.io/mlops/pipeline/internal/config"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

type MetricQueryTmplName string

const (
	PodCPUUsage       MetricQueryTmplName = "pod_cpu_usage"
	PodMemoryUsage    MetricQueryTmplName = "pod_memory_usage"
	PodXPUUsage       MetricQueryTmplName = "pod_xpu_usage"
	PodXPUMemoryUsage MetricQueryTmplName = "pod_xpu_memory_usage"
)

const (
	MetricLabelRefPodName      = "ref_pod_name"
	MetricLabelRefPodNamespace = "ref_pod_namespace"
	MetricLabelDeviceType      = "devicetype"
)

type CacheCollector struct {
	cache     *Cache
	collector *MetricsCollector
	ttl       time.Duration
	interval  time.Duration
	stopCh    chan struct{}
}

func NewCacheCollector(collector *MetricsCollector, ttl, interval, cleanupInterval time.Duration) *CacheCollector {
	return &CacheCollector{
		cache:     NewCache(ttl, cleanupInterval),
		collector: collector,
		interval:  interval,
		stopCh:    make(chan struct{}),
	}
}

type CacheDataProviderFunc func(ctx context.Context) ([]*PodResourceUsage, error)

func (mc *CacheCollector) Start(ctx context.Context, provider CacheDataProviderFunc) {
	// initial refresh
	zlog.SugarInfoln("Initial refresh all cache metrics.")
	mc.refresh(ctx, provider)

	ticker := time.NewTicker(mc.interval)
	go func() {
		for {
			select {
			case <-ticker.C:
				mc.refresh(ctx, provider)
			case <-ctx.Done():
				ticker.Stop()
				close(mc.stopCh)
				return
			}
		}
	}()
}

func (mc *CacheCollector) refresh(ctx context.Context, provider CacheDataProviderFunc) {
	usages, err := provider(ctx)
	if err != nil {
		zlog.SugarErrorf("Failed to refresh all cache metrics: %v", err)
		return
	}
	for _, usage := range usages {
		mc.cache.Set(CacheKey(usage.Namespace, usage.PodName), usage, mc.ttl)
	}
}

func (mc *CacheCollector) Stop() {
	close(mc.stopCh)
}

func (mc *CacheCollector) Get(key string) (*PodResourceUsage, bool) {
	return mc.cache.Get(key)
}

type MetricsCollector struct {
	queryEngine *QueryEngine
	config      *config.MetricsConfig
	cache       *CacheCollector
}

func NewMetricsCollector(queryEngine *QueryEngine, config *config.MetricsConfig) *MetricsCollector {
	mc := &MetricsCollector{
		queryEngine: queryEngine,
		config:      config,
	}

	if config.Cache.Enabled {
		mc.cache = NewCacheCollector(mc, config.Cache.TTL, config.Cache.Interval, config.Cache.CleanupInterval)
		mc.cache.Start(context.Background(), mc.collectAllPodResourceUsage)
	}

	return mc
}
func (mc *MetricsCollector) collectAllPodResourceUsage(ctx context.Context) ([]*PodResourceUsage, error) {
	return mc.QueryPodResourceUsageUsingTmplWithFilters(ctx, &QueryParams{})
}
func (mc *MetricsCollector) queryCache(ctx context.Context, filters *QueryParams) ([]*PodResourceUsage, bool) {
	if usage, exists := mc.cache.Get(CacheKey(filters.Namespace, filters.PodName)); exists {
		return []*PodResourceUsage{usage}, true
	}
	return nil, false
}

func (mc *MetricsCollector) QueryPodResourceUsageUsingTmplWithFilters(ctx context.Context, filters *QueryParams) ([]*PodResourceUsage, error) {
	if mc.config.Cache.Enabled {
		if usages, exists := mc.queryCache(ctx, filters); exists {
			return usages, nil
		}
		return nil, fmt.Errorf("no metric found for pod %s in namespace %s from cache", filters.PodName, filters.Namespace)
	}

	results, err := mc.collectResults(ctx, filters,
		mc.queryPodCPUUsageUsingTmpl,
		mc.queryPodMemoryUsageUsingTmpl,
		mc.queryPodXPUUsageUsingTmpl,
		mc.queryPodXPUMemoryUsageUsingTmpl,
	)
	if err != nil {
		return nil, err
	}

	finalUsages := make([]*PodResourceUsage, 0, len(results))
	for _, usage := range results {
		finalUsages = append(finalUsages, usage)
	}

	return finalUsages, nil
}

func (mc *MetricsCollector) queryPodCPUUsageUsingTmpl(ctx context.Context, filters *QueryParams) ([]*PodResourceUsage, error) {
	result, err := mc.queryEngine.QueryUsingTmpl(ctx, string(PodCPUUsage), filters, time.Now())
	if err != nil {
		return nil, err
	}

	var usages []*PodResourceUsage
	for _, point := range result.Data {
		usage := &PodResourceUsage{
			PodName:   point.Labels[MetricLabelRefPodName],
			Namespace: point.Labels[MetricLabelRefPodNamespace],
			CPUUsage:  point.Value,
			Timestamp: point.Timestamp,
		}
		usages = append(usages, usage)
	}

	return usages, nil
}

func (mc *MetricsCollector) queryPodMemoryUsageUsingTmpl(ctx context.Context, filters *QueryParams) ([]*PodResourceUsage, error) {
	result, err := mc.queryEngine.QueryUsingTmpl(ctx, string(PodMemoryUsage), filters, time.Now())
	if err != nil {
		return nil, err
	}

	var usages []*PodResourceUsage
	for _, point := range result.Data {
		usage := &PodResourceUsage{
			PodName:     point.Labels[MetricLabelRefPodName],
			Namespace:   point.Labels[MetricLabelRefPodNamespace],
			MemoryUsage: point.Value,
			Timestamp:   point.Timestamp,
		}
		usages = append(usages, usage)
	}

	return usages, nil
}

func (mc *MetricsCollector) queryPodXPUUsageUsingTmpl(ctx context.Context, filters *QueryParams) ([]*PodResourceUsage, error) {
	result, err := mc.queryEngine.QueryUsingTmpl(ctx, string(PodXPUUsage), filters, time.Now())
	if err != nil {
		return nil, err
	}

	var usages []*PodResourceUsage
	for _, point := range result.Data {
		usage := &PodResourceUsage{
			PodName:    point.Labels[MetricLabelRefPodName],
			Namespace:  point.Labels[MetricLabelRefPodNamespace],
			DeviceType: mc.shortDeviceType(point.Labels[MetricLabelDeviceType]),
			XPUUsage:   point.Value,
			Timestamp:  point.Timestamp,
		}
		usages = append(usages, usage)
	}

	return usages, nil
}

func (mc *MetricsCollector) queryPodXPUMemoryUsageUsingTmpl(ctx context.Context, filters *QueryParams) ([]*PodResourceUsage, error) {
	result, err := mc.queryEngine.QueryUsingTmpl(ctx, string(PodXPUMemoryUsage), filters, time.Now())
	if err != nil {
		return nil, err
	}

	var usages []*PodResourceUsage
	for _, point := range result.Data {
		usage := &PodResourceUsage{
			PodName:        point.Labels[MetricLabelRefPodName],
			Namespace:      point.Labels[MetricLabelRefPodNamespace],
			DeviceType:     mc.shortDeviceType(point.Labels[MetricLabelDeviceType]),
			XPUMemoryUsage: point.Value,
			Timestamp:      point.Timestamp,
		}
		usages = append(usages, usage)
	}

	return usages, nil
}

func (mc *MetricsCollector) shortDeviceType(deviceType string) string {
	re := regexp.MustCompile("[-_ ]+")
	parts := re.Split(deviceType, -1)

	if len(parts) > 0 {
		return strings.ToLower(parts[0])
	} else {
		zlog.SugarWarnf("Failed to short device type: %s", deviceType)
	}
	return strings.ToLower(deviceType)
}

type QueryFunc func(ctx context.Context, filters *QueryParams) ([]*PodResourceUsage, error)

func (mc *MetricsCollector) collectResults(ctx context.Context, filters *QueryParams, queries ...QueryFunc) (map[string]*PodResourceUsage, error) {
	type QueryResult struct {
		Usages []*PodResourceUsage
		Err    error
	}

	var wg sync.WaitGroup
	resultsChan := make(chan QueryResult, len(queries))

	for _, query := range queries {
		wg.Add(1)
		go func(q QueryFunc) {
			defer wg.Done()
			usages, err := q(ctx, filters)
			resultsChan <- QueryResult{Usages: usages, Err: err}
		}(query)
	}

	wg.Wait()
	close(resultsChan)

	mergedUsages := make(map[string]*PodResourceUsage)

	for res := range resultsChan {
		if res.Err != nil {
			return nil, res.Err
		}
		for _, u := range res.Usages {
			if usage, ok := mergedUsages[u.PodName]; ok {
				usage.CPUUsage += u.CPUUsage
				usage.MemoryUsage += u.MemoryUsage
			} else {
				mergedUsages[u.PodName] = u
			}
		}
	}

	return mergedUsages, nil
}
