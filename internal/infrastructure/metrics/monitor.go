package metrics

import (
	"context"
	"fmt"
	"sync"

	config "transwarp.io/mlops/pipeline/internal/config"
)

var (
	metricMonitor *MetricsMonitor
	once          sync.Once
)

type MetricsMonitor struct {
	config      *config.MetricsConfig
	queryEngine *QueryEngine
	collector   *MetricsCollector

	ctx    context.Context
	cancel context.CancelFunc
}

func MustGetMetricMonitor() *MetricsMonitor {
	once.Do(func() {
		mon, err := newMetricsMonitor(config.MustGetMetricsConfig())
		if err != nil {
			panic(fmt.Sprintf("failed to initialize MetricsMonitor: %v", err))
		}

		metricMonitor = mon
	})

	return metricMonitor
}

func newMetricsMonitor(config *config.MetricsConfig) (*MetricsMonitor, error) {
	queryEngine, err := NewQueryEngine(&config.Prometheus)
	if err != nil {
		return nil, fmt.Errorf("failed to create query engine: %w", err)
	}
	for name, metric := range config.Metrics {
		if err := queryEngine.RegisterTemplate(name, metric.Query); err != nil {
			return nil, fmt.Errorf("failed to register template %s: %w", name, err)
		}
	}

	collector := NewMetricsCollector(queryEngine, config)

	return &MetricsMonitor{
		config:      config,
		queryEngine: queryEngine,
		collector:   collector,
	}, nil
}

// func (mm *MetricsMonitor) QueryPodMetricsByNamespace(ctx context.Context, namespace string) ([]*PodResourceUsage, error) {
// 	return mm.collector.QueryPodResourceUsageUsingTmplWithFilters(ctx, &QueryParams{
// 		Namespace: namespace,
// 	})
// }

func (mm *MetricsMonitor) QueryPodResourceUsage(ctx context.Context, namespace, podName string) (*PodResourceUsage, error) {
	podUsage, err := mm.collector.QueryPodResourceUsageUsingTmplWithFilters(ctx, &QueryParams{
		Namespace: namespace,
		PodName:   podName,
	})
	if err != nil {
		return nil, err
	}
	if len(podUsage) > 0 {
		return podUsage[0], nil
	}
	return nil, fmt.Errorf("no metric found for pod %s in namespace %s", podName, namespace)
}
