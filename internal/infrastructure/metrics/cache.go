package metrics

import (
	"sync"
	"time"

	gocache "github.com/patrickmn/go-cache"
)

type Cache struct {
	cache *gocache.Cache
	mu    sync.RWMutex
}

func NewCache(ttl time.Duration, cleanupInterval time.Duration) *Cache {
	return &Cache{
		cache: gocache.New(ttl, cleanupInterval),
		mu:    sync.RWMutex{},
	}
}

func (c *Cache) Set(key string, usage *PodResourceUsage, ttl time.Duration) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.cache.Set(key, usage, ttl)
}

func (c *Cache) Get(key string) (*PodResourceUsage, bool) {
	if value, exists := c.cache.Get(key); exists {
		return value.(*PodResourceUsage), true
	}
	return nil, false
}

func (c *Cache) Exist(key string) bool {
	_, exists := c.cache.Get(key)
	return exists
}

func CacheKey(namespace, podName string) string {
	return namespace + "/" + podName
}
