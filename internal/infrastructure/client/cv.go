package client

import (
	"context"
	"fmt"
	"os"
	"strconv"
	"sync"

	tokenutil "transwarp.io/mlops/mlops-std/token"
	conf "transwarp.io/mlops/pipeline/internal/config"
	"transwarp.io/mlops/pipeline/pkg/util/httpclient"
)

const (
	CVHttpClientName = "cv"

	CVURL = "http://autocv-cvat-service:80"
)

var (
	cvHTTPClientInstance *CVHttpClient
	cvOnce               sync.Once
)

type CVHttpClient struct {
	*httpclient.HttpClient
}

func MustGetCVHTTPClient() *CVHttpClient {
	client, err := GetCVHTTPClient()
	if err != nil {
		panic(fmt.Sprintf("failed to get CV client: %v", err))
	}
	return client
}

func GetCVHTTPClient() (*CVHttpClient, error) {
	var initErr error
	cvOnce.Do(func() {
		url := CVURL
		if enableDebugStr := os.Getenv("LOCAL_DEBUG_MODEL"); enableDebugStr != "" {
			if enableDebug, err := strconv.ParseBool(enableDebugStr); err == nil || enableDebug {
				if cvAddress := os.Getenv("CV_ADDRESS"); cvAddress != "" {
					url = cvAddress
				}
			}
		} else if conf.PipeineConfig.Pipeline.Cvat.Host != "" {
			url = fmt.Sprintf("http://%s:%s", conf.PipeineConfig.Pipeline.Cvat.Host, conf.PipeineConfig.Pipeline.Cvat.Port)
		}
		cvHTTPClientInstance = &CVHttpClient{
			HttpClient: httpclient.NewHttpClient(url,
				httpclient.WithInsecureSkipVerify()),
		}
	})

	return cvHTTPClientInstance, initErr
}

type MLOpsServiceBaseInfo struct {
	Id         string `json:"id"`
	Name       string `json:"name"`
	ProjectID  string `json:"project_id"`
	SourceType string `json:"source_type"` // 来源类型 [cv.SourceType], 接口返回的是枚举的变量名
}

type MLOpsServiceList struct {
	MLOpsServiceInfos []*MLOpsServiceBaseInfo `protobuf:"bytes,3,rep,name=service_infos,json=serviceInfos,proto3" json:"service_infos" description:"服务基础信息"`
}

type TaskDetail struct {
	ID            string        `json:"id"`
	Name          string        `json:"name"`
	Status        string        `json:"status"`
	ProjectID     string        `json:"projectId"`
	TenantUID     string        `json:"tenantUid"`
	CatalogParams CatalogParams `json:"catalogParams"`
	AssetFiles    []AssetFile   `json:"assetFiles"`
	KBPTmplType   string        `json:"kBPTmplType"`
}

type CatalogParams struct {
	CatalogID string `json:"catalogId"`
	LabelIDs  []int  `json:"labelIDs"`
}

type AssetFile struct {
	AssetID        string `json:"assetId"`
	AssetVersionID string `json:"assetVersionId"`
}

func (c *CVHttpClient) GetTaskDetail(ctx context.Context, taskId string) (*TaskDetail, error) {
	path := fmt.Sprintf("/api/process/tasks/%s/detail", taskId)

	headers, err := getTokenHeader(ctx)
	if err != nil {
		return nil, err
	}
	result, err := httpclient.GetT[TaskDetail](ctx, c.HttpClient, path, nil, headers)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func getTokenHeader(ctx context.Context) (map[string]string, error) {
	token, err := tokenutil.GetToken(ctx)
	if err != nil {
		return nil, err
	}
	return map[string]string{
		tokenutil.AUTH_HEADER: token,
	}, nil
}
