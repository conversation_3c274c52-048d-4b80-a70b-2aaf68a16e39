package client

import (
	"context"
	"fmt"
	"sync"
	"sync/atomic"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/tools/cache"
	jobsetv1alpha2 "sigs.k8s.io/jobset/api/jobset/v1alpha2"
	jobsetclientset "sigs.k8s.io/jobset/client-go/clientset/versioned"
	jobsetinformers "sigs.k8s.io/jobset/client-go/informers/externalversions"
	jobsetlisters "sigs.k8s.io/jobset/client-go/listers/jobset/v1alpha2"

	util "transwarp.io/mlops/pipeline/internal/util"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

const (
	JobSetClientName = "jobset"

	JobSetApiVersion = "jobset.x-k8s.io/v1alpha2"
	JobSetKind       = "JobSet"
)

var (
	JobSetGVK = jobsetv1alpha2.SchemeGroupVersion.WithKind("JobSet")

	jobSetClientInstance *JobSetClient
	jobSetOnce           sync.Once
)

type JobSetClient struct {
	clientset       jobsetclientset.Interface
	informerFactory jobsetinformers.SharedInformerFactory

	// Listers
	jobSetLister jobsetlisters.JobSetLister

	// Informers
	jobSetInformer cache.SharedIndexInformer

	eventHandlers map[string][]cache.ResourceEventHandler

	stopCh        chan struct{}
	enabledJobSet atomic.Bool
	once          sync.Once
}

func MustGetJobSetClient() *JobSetClient {
	client, err := GetJobSetClient()
	if err != nil {
		panic(fmt.Sprintf("failed to get JobSet client: %v", err))
	}
	return client
}

func GetJobSetClient() (*JobSetClient, error) {
	var initErr error
	jobSetOnce.Do(func() {
		config, err := util.GetKubeConfig()
		if err != nil {
			zlog.SugarErrorf("Error building jobset client config: %v", err)
			initErr = err
			return
		}

		clientset := jobsetclientset.NewForConfigOrDie(config)
		jobSetClientInstance = &JobSetClient{
			clientset:       clientset,
			informerFactory: jobsetinformers.NewSharedInformerFactory(clientset, DefaultResyncPeriod),
			eventHandlers:   make(map[string][]cache.ResourceEventHandler),
			stopCh:          make(chan struct{}),
		}

		if jobSetClientInstance.IsJobSetSupported() {
			if err := jobSetClientInstance.setupAndSyncInformers(context.Background()); err != nil {
				zlog.SugarWarnf("setup jobset informer failed: %v", err)
			}
		}
	})
	return jobSetClientInstance, initErr
}

func (jc *JobSetClient) WaitForCacheSync(ctx context.Context) bool {
	return cache.WaitForCacheSync(ctx.Done(),
		jc.jobSetInformer.HasSynced,
	)
}

func (jc *JobSetClient) setupAndSyncInformers(ctx context.Context) error {
	if jc.informerFactory == nil {
		zlog.SugarErrorf("Informer factory not initialized, skipping informer setup")
		return fmt.Errorf("informer factory not initialized")
	}

	// JobSet Informer
	jc.jobSetInformer = jc.informerFactory.Jobset().V1alpha2().JobSets().Informer()
	jc.jobSetLister = jc.informerFactory.Jobset().V1alpha2().JobSets().Lister()

	go jc.informerFactory.Start(jc.stopCh)

	syncCtx, cancel := context.WithTimeout(ctx, DefaultInformerSyncTimeout)
	defer cancel()

	if !jc.WaitForCacheSync(syncCtx) {
		return fmt.Errorf("timed out waiting for JobSet cache sync. JobSet may not be installed or reachable")
	}

	zlog.SugarInfoln("JobSet informers cache synced successfully.")
	return nil
}

func (c *JobSetClient) IsJobSetSupported() bool {
	c.once.Do(func() {
		if support := util.SupportAPIResources(MustGetK8sClient().GetDiscoveryClient(), JobSetApiVersion, JobSetKind); support {
			c.enabledJobSet.Store(true)
		} else {
			c.enabledJobSet.Store(false)
		}
	})

	return c.enabledJobSet.Load()
}

func (jc *JobSetClient) GetJobSetInformer() cache.SharedIndexInformer {
	return jc.jobSetInformer
}

func (jc *JobSetClient) AddJobSetEventHandler(handler cache.ResourceEventHandler) {
	jc.jobSetInformer.AddEventHandler(handler)
	jc.eventHandlers["jobset"] = append(jc.eventHandlers["jobset"], handler)
}

func (jc *JobSetClient) ListJobSets(namespace string) ([]*jobsetv1alpha2.JobSet, error) {
	if namespace == "" {
		return jc.jobSetLister.List(labels.Everything())
	}
	return jc.jobSetLister.JobSets(namespace).List(labels.Everything())
}

func (jc *JobSetClient) GetJobSet(namespace, name string) (*jobsetv1alpha2.JobSet, error) {
	jobSet, err := jc.jobSetLister.JobSets(namespace).Get(name)
	if err != nil {
		return nil, err
	}
	jobSet.SetGroupVersionKind(JobSetGVK)
	return jobSet, nil
}

func (jc *JobSetClient) ListJobSetsBySelector(namespace string, selector labels.Selector) ([]*jobsetv1alpha2.JobSet, error) {
	if namespace == "" {
		return jc.jobSetLister.List(selector)
	}
	return jc.jobSetLister.JobSets(namespace).List(selector)
}

func (jc *JobSetClient) CreateJobSet(ctx context.Context, jobSet *jobsetv1alpha2.JobSet) (*jobsetv1alpha2.JobSet, error) {
	return jc.clientset.JobsetV1alpha2().JobSets(jobSet.Namespace).Create(ctx, jobSet, metav1.CreateOptions{})
}

func (jc *JobSetClient) UpdateJobSet(ctx context.Context, jobSet *jobsetv1alpha2.JobSet) (*jobsetv1alpha2.JobSet, error) {
	return jc.clientset.JobsetV1alpha2().JobSets(jobSet.Namespace).Update(ctx, jobSet, metav1.UpdateOptions{})
}

func (jc *JobSetClient) DeleteJobSet(ctx context.Context, namespace, name string) error {
	return jc.clientset.JobsetV1alpha2().JobSets(namespace).Delete(ctx, name, metav1.DeleteOptions{})
}

func (jc *JobSetClient) Stop() {
	close(jc.stopCh)
}
