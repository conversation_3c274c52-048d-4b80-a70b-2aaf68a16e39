package client

import (
	"context"
	"fmt"
	"sync"
	"sync/atomic"

	"k8s.io/client-go/tools/cache"
	lwsapi "sigs.k8s.io/lws/api/leaderworkerset/v1"
	lwsclientset "sigs.k8s.io/lws/client-go/clientset/versioned"
	lwsinformers "sigs.k8s.io/lws/client-go/informers/externalversions"
	lwslisters "sigs.k8s.io/lws/client-go/listers/leaderworkerset/v1"

	util "transwarp.io/mlops/pipeline/internal/util"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

const (
	LWSClientName = "lws"
	LWSApiVersion = "leaderworkerset.x-k8s.io/v1"
	LWSKind       = "LeaderWorkerSet"
)

var (
	LeaderWorkerSetGVK = lwsapi.SchemeGroupVersion.WithKind("LeaderWorkerSet")

	lwsClientInstance *LeaderWorkerSetClient
	lwsOnce           sync.Once
)

type LeaderWorkerSetClient struct {
	clientset       lwsclientset.Interface
	informerFactory lwsinformers.SharedInformerFactory

	// Informers
	LeaderWorkerSetInformer cache.SharedIndexInformer

	LeaderWorkerSetLister lwslisters.LeaderWorkerSetLister

	eventHandlers map[string][]cache.ResourceEventHandler

	stopCh chan struct{}

	enabledLWS atomic.Bool
	once       sync.Once
}

func MustGetLeaderWorkerSetClient() *LeaderWorkerSetClient {
	client, err := GetLeaderWorkerSetClient()
	if err != nil {
		panic(fmt.Sprintf("failed to get LeaderWorkerSet client: %v", err))
	}
	return client
}

func GetLeaderWorkerSetClient() (*LeaderWorkerSetClient, error) {
	var initErr error
	lwsOnce.Do(func() {
		config, err := util.GetKubeConfig()
		if err != nil {
			zlog.SugarErrorf("Error building lws client config: %v", err)
			initErr = err
			return
		}

		clientset := lwsclientset.NewForConfigOrDie(config)
		lwsClientInstance = &LeaderWorkerSetClient{
			clientset:       clientset,
			informerFactory: lwsinformers.NewSharedInformerFactory(clientset, DefaultResyncPeriod),
			eventHandlers:   make(map[string][]cache.ResourceEventHandler),
			stopCh:          make(chan struct{}),
		}

		if lwsClientInstance.IsLWSSupported() {
			if err := lwsClientInstance.setupAndSyncInformers(context.Background()); err != nil {
				zlog.SugarWarnf("setup lws informer failed: %v", err)
			}
		}
	})
	return lwsClientInstance, initErr
}

func (c *LeaderWorkerSetClient) WaitForCacheSync(ctx context.Context) bool {
	return cache.WaitForCacheSync(ctx.Done(),
		c.LeaderWorkerSetInformer.HasSynced,
	)
}

func (c *LeaderWorkerSetClient) setupAndSyncInformers(ctx context.Context) error {
	if c.informerFactory == nil {
		zlog.SugarErrorf("Informer factory not initialized, skipping informer setup")
		return fmt.Errorf("informer factory not initialized")
	}

	c.LeaderWorkerSetInformer = c.informerFactory.Leaderworkerset().V1().LeaderWorkerSets().Informer()

	c.LeaderWorkerSetLister = c.informerFactory.Leaderworkerset().V1().LeaderWorkerSets().Lister()

	go c.informerFactory.Start(c.stopCh)

	syncCtx, cancel := context.WithTimeout(ctx, DefaultInformerSyncTimeout) // Use a context derived from the input context
	defer cancel()

	if !c.WaitForCacheSync(syncCtx) {
		return fmt.Errorf("timed out waiting for lws cache sync. LWS may not be installed or reachable")
	}

	zlog.SugarInfoln("LWS informers cache synced successfully.")
	return nil
}

func (c *LeaderWorkerSetClient) IsLWSSupported() bool {
	c.once.Do(func() {
		if support := util.SupportAPIResources(MustGetK8sClient().GetDiscoveryClient(), LWSApiVersion, LWSKind); support {
			c.enabledLWS.Store(true)
		} else {
			c.enabledLWS.Store(false)
		}
	})

	return c.enabledLWS.Load()
}

func (c *LeaderWorkerSetClient) AddLeaderWorkerSetEventHandler(handler cache.ResourceEventHandler) {
	c.LeaderWorkerSetInformer.AddEventHandler(handler)
	c.eventHandlers["leaderworkerset"] = append(c.eventHandlers["leaderworkerset"], handler)
}

func (c *LeaderWorkerSetClient) GetLeaderWorkerSet(namespace string, name string) (*lwsapi.LeaderWorkerSet, error) {
	lws, err := c.LeaderWorkerSetLister.LeaderWorkerSets(namespace).Get(name)
	if err != nil {
		return nil, err
	}
	lws.SetGroupVersionKind(LeaderWorkerSetGVK)
	return lws, nil
}

func (c *LeaderWorkerSetClient) Stop() {
	close(c.stopCh)
}
