package client

import (
	"bytes"
	"context"
	"fmt"
	"strings"
	"sync"

	zlog "transwarp.io/mlops/pipeline/pkg/util/log"

	eventsv1alpha1 "github.com/argoproj/argo-events/pkg/apis/events/v1alpha1"
	clientset "github.com/argoproj/argo-events/pkg/client/clientset/versioned"
	versionedclient "github.com/argoproj/argo-events/pkg/client/clientset/versioned"
	argoeventinforemer "github.com/argoproj/argo-events/pkg/client/informers/externalversions"
	listers "github.com/argoproj/argo-events/pkg/client/listers/events/v1alpha1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/fields"
	"k8s.io/utils/ptr"
	pb "transwarp.io/aip/llmops-common/pb/pipeline"
	"transwarp.io/applied-ai/aiot/vision-std/boot/k8s"
	client "transwarp.io/mlops/mlops-std/k8s"
	"transwarp.io/mlops/mlops-std/stderr"
	conf "transwarp.io/mlops/pipeline/internal/config"
)

const (
	ArgoEventClientName = "argo-event"
)

var (
	SensorGVK      = eventsv1alpha1.SchemeGroupVersion.WithKind("Sensor")
	EventSourceGVK = eventsv1alpha1.SchemeGroupVersion.WithKind("EventSource")
	EventBusGVK    = eventsv1alpha1.SchemeGroupVersion.WithKind("EventBus")

	argoEventClientInstance *ArgoEventClient
	argoEventOnce           sync.Once
)

type ArgoEventClient struct {
	SensorLister listers.SensorLister
	ebLister     listers.EventBusLister
	esLister     listers.EventSourceLister
	ClientSet    *clientset.Clientset
	stopCh       chan struct{}
}

func MustGetArgoEventClient() *ArgoEventClient {
	client, err := GetArgoEventClient()
	if err != nil {
		panic(fmt.Sprintf("failed to get ArgoEvent client: %v", err))
	}
	return client
}

func GetArgoEventClient() (*ArgoEventClient, error) {
	var initErr error
	argoEventOnce.Do(func() {
		argoEventClientInstance, initErr = newArgoEventClient()
	})
	return argoEventClientInstance, initErr
}

func newArgoEventClient() (*ArgoEventClient, error) {
	client := &ArgoEventClient{}
	client.Init()
	return client, nil
}

func (s *ArgoEventClient) Init() error {
	config, err := client.GetK8sRestConfig()
	if err != nil {
		return stderr.Wrap(err, "init argo cli err :%v", err)
	}
	s.ClientSet, err = versionedclient.NewForConfig(config)
	if err != nil {
		return stderr.Wrap(err, "init argo cli err :%v", err)
	}
	factory := argoeventinforemer.NewSharedInformerFactoryWithOptions(
		s.ClientSet,
		DefaultResyncPeriod, // resync 周期
		argoeventinforemer.WithTweakListOptions(func(opts *metav1.ListOptions) {
			opts.FieldSelector = fields.Everything().String()
		}),
	)

	s.SensorLister = factory.Argoproj().V1alpha1().Sensors().Lister()
	s.ebLister = factory.Argoproj().V1alpha1().EventBus().Lister()
	s.esLister = factory.Argoproj().V1alpha1().EventSources().Lister()
	// 启动 informer
	s.stopCh = make(chan struct{}) // 创建 stopCh
	go factory.Start(s.stopCh)

	// 等待缓存同步完成
	synced := factory.WaitForCacheSync(s.stopCh)
	for t, ok := range synced {
		if !ok {
			return fmt.Errorf("cache for %v failed to sync", t)
		}
	}
	return nil
}

func (s *ArgoEventClient) InitNamespaceResource(namespace string, projectId string) error {

	err := s.initSecret(namespace)
	if err != nil {
		zlog.SugarErrorf("error in init namespace %s sc: %+v", namespace, err)
		return stderr.Internal.Errorf("error in init namespace %s sc: %s", namespace, err.Error())
	}
	err = s.initEventBus(namespace)
	if err != nil {
		zlog.SugarErrorf("error in init namespace %s eb: %+v", namespace, err)
		return stderr.Internal.Errorf("error in init namespace %s eb: %s", namespace, err.Error())
	}
	err = s.initEventSource(namespace, projectId)
	if err != nil {
		zlog.SugarErrorf("error in init namespace %s es: %+v", namespace, err)
		return stderr.Internal.Errorf("error in init namespace %s es: %s", namespace, err.Error())
	}
	return nil
}

func (s *ArgoEventClient) initEventBus(namespace string) error {
	eb := defaultEventBus(namespace)
	_, err := s.ebLister.EventBus(namespace).Get(eb.Name)
	if err != nil && !errors.IsNotFound(err) {
		return err
	}
	if errors.IsNotFound(err) {
		zlog.SugarInfof("namespace %s eb %s no exist, create ...", namespace, eb.Name)
		_, err = s.ClientSet.ArgoprojV1alpha1().EventBus(namespace).Create(context.Background(), eb, metav1.CreateOptions{})
		return err
	}
	return nil
}
func (s *ArgoEventClient) initEventSource(namespace string, projectId string) error {
	es := defaultEventSource(namespace, projectId)
	_, err := s.esLister.EventSources(namespace).Get(es.Name)
	if err != nil && !errors.IsNotFound(err) {
		return err
	}
	if errors.IsNotFound(err) {
		zlog.SugarInfof("namespace %s es %s no exist, create ...", namespace, es.Name)
		_, err = s.ClientSet.ArgoprojV1alpha1().EventSources(namespace).Create(context.Background(), es, metav1.CreateOptions{})
		return err
	}
	return nil
}
func (s *ArgoEventClient) initSecret(namespace string) error {
	tarSecret, err := MustGetK8sClient().SecretLister.Secrets(k8s.CurrentNamespaceInCluster()).Get("llmops-secret")
	if err != nil {
		return err
	}
	secret, err := MustGetK8sClient().SecretLister.Secrets(namespace).Get("llmops-secret")
	if err != nil && !errors.IsNotFound(err) {
		return err
	}
	if errors.IsNotFound(err) {
		zlog.SugarInfof("namespace %s secret %s no exist, create ...", namespace, "llmops-secret")
		tarSecret.ObjectMeta = metav1.ObjectMeta{
			Name:      tarSecret.Name,
			Namespace: namespace,
		}
		_, err := MustGetK8sClient().Clientset.CoreV1().Secrets(namespace).Create(context.Background(), tarSecret, metav1.CreateOptions{})
		return err
	}
	if len(secret.Data["redis_password"]) == 0 || !bytes.Equal(tarSecret.Data["redis_password"], secret.Data["redis_password"]) {
		zlog.SugarInfof("namespace %s secret %s redis password is invalid, updating ...", namespace, "llmops-secret")
		secret.Data = tarSecret.Data
	}
	return nil
}

func defaultEventBus(namespace string) *eventsv1alpha1.EventBus {
	return &eventsv1alpha1.EventBus{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "default",
			Namespace: namespace,
		},
		Spec: eventsv1alpha1.EventBusSpec{
			JetStream: &eventsv1alpha1.JetStreamBus{
				Version:  "2.10.10",
				Replicas: ptr.To(int32(3)),
			},
		},
	}
}

func defaultEventSource(namespace string, projectId string) *eventsv1alpha1.EventSource {
	return &eventsv1alpha1.EventSource{
		ObjectMeta: metav1.ObjectMeta{
			Name:      strings.ToLower(pb.EventType_FileAssets.String()),
			Namespace: namespace,
		},
		Spec: eventsv1alpha1.EventSourceSpec{
			Redis: map[string]eventsv1alpha1.RedisEventSource{
				"message": {
					HostAddress: conf.PipeineConfig.Pipeline.Redis.Addrs,
					Channels: []string{
						fmt.Sprintf("event-channel-%s-%s", k8s.CurrentNamespaceInCluster(), projectId),
					},
					Password: &corev1.SecretKeySelector{
						Key: "redis_password",
						LocalObjectReference: corev1.LocalObjectReference{
							Name: "llmops-secret",
						},
					},
					Namespace: namespace,
					JSONBody:  true,
					DB:        int32(conf.PipeineConfig.Pipeline.Redis.Database),
					Username:  conf.PipeineConfig.Pipeline.Redis.Username,
				},
			},
		},
	}
}

func (c *ArgoEventClient) GetEventBus(namespace, name string) (*eventsv1alpha1.EventBus, error) {
	eventBus, err := c.ebLister.EventBus(namespace).Get(name)
	if err != nil {
		return nil, err
	}
	eventBus.SetGroupVersionKind(EventBusGVK)
	return eventBus, nil
}
