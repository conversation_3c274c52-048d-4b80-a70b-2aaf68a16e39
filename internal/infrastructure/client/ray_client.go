package client

import (
	"context"
	"fmt"
	"sync"
	"sync/atomic"

	rayv1 "github.com/ray-project/kuberay/ray-operator/apis/ray/v1"
	rayclientset "github.com/ray-project/kuberay/ray-operator/pkg/client/clientset/versioned"
	rayinformers "github.com/ray-project/kuberay/ray-operator/pkg/client/informers/externalversions"
	raylisters "github.com/ray-project/kuberay/ray-operator/pkg/client/listers/ray/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/tools/cache"

	util "transwarp.io/mlops/pipeline/internal/util"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

const (
	RayClientName = "ray"

	RayApiVersion  = "ray.io/v1"
	RayServiceKind = "RayService"
)

var (
	RayJobGVK     = rayv1.SchemeGroupVersion.WithKind("RayJob")
	RayClusterGVK = rayv1.SchemeGroupVersion.WithKind("RayCluster")
	RayServiceGVK = rayv1.SchemeGroupVersion.WithKind("RayService")

	rayClientInstance *RayClient
	rayOnce           sync.Once
)

type RayClient struct {
	clientset       rayclientset.Interface
	informerFactory rayinformers.SharedInformerFactory

	// Listers
	rayJobLister     raylisters.RayJobLister
	rayClusterLister raylisters.RayClusterLister
	rayServiceLister raylisters.RayServiceLister

	// Informers
	rayJobInformer     cache.SharedIndexInformer
	rayClusterInformer cache.SharedIndexInformer
	rayServiceInformer cache.SharedIndexInformer

	eventHandlers map[string][]cache.ResourceEventHandler

	stopCh chan struct{}

	enabledRay atomic.Bool
	once       sync.Once
}

func MustGetRayClient() *RayClient {
	client, err := GetRayClient()
	if err != nil {
		panic(fmt.Sprintf("failed to get Ray client: %v", err))
	}
	return client
}

func GetRayClient() (*RayClient, error) {
	var initErr error
	rayOnce.Do(func() {
		config, err := util.GetKubeConfig()
		if err != nil {
			zlog.SugarErrorf("Error building ray client config: %v", err)
			initErr = err
			return
		}

		clientset := rayclientset.NewForConfigOrDie(config)
		rayClientInstance = &RayClient{
			clientset:       clientset,
			informerFactory: rayinformers.NewSharedInformerFactory(clientset, DefaultResyncPeriod),
			eventHandlers:   make(map[string][]cache.ResourceEventHandler),
			stopCh:          make(chan struct{}),
		}

		if rayClientInstance.IsRaySupported() {
			if err := rayClientInstance.setupAndSyncInformers(context.Background()); err != nil {
				zlog.SugarWarnf("setup ray informer failed: %v", err)
			}
		}
	})
	return rayClientInstance, initErr
}

func (rc *RayClient) WaitForCacheSync(ctx context.Context) bool {
	return cache.WaitForCacheSync(ctx.Done(),
		rc.rayJobInformer.HasSynced,
		rc.rayClusterInformer.HasSynced,
		rc.rayServiceInformer.HasSynced,
	)
}

func (rc *RayClient) setupAndSyncInformers(ctx context.Context) error {
	if rc.informerFactory == nil {
		zlog.SugarErrorf("Informer factory not initialized, skipping informer setup")
		return fmt.Errorf("informer factory not initialized")
	}

	// RayJob Informer
	rc.rayJobInformer = rc.informerFactory.Ray().V1().RayJobs().Informer()
	rc.rayJobLister = rc.informerFactory.Ray().V1().RayJobs().Lister()

	// RayCluster Informer
	rc.rayClusterInformer = rc.informerFactory.Ray().V1().RayClusters().Informer()
	rc.rayClusterLister = rc.informerFactory.Ray().V1().RayClusters().Lister()

	// RayService Informer
	rc.rayServiceInformer = rc.informerFactory.Ray().V1().RayServices().Informer()
	rc.rayServiceLister = rc.informerFactory.Ray().V1().RayServices().Lister()

	go rc.informerFactory.Start(rc.stopCh)

	syncCtx, cancel := context.WithTimeout(ctx, DefaultInformerSyncTimeout)
	defer cancel()
	if !rc.WaitForCacheSync(syncCtx) {
		return fmt.Errorf("timed out waiting for Ray cache sync. KubeRay may not be installed or reachable")
	}
	zlog.SugarInfoln("Ray informers cache synced successfully.")
	return nil
}

func (rc *RayClient) IsRaySupported() bool {
	rc.once.Do(func() {
		if support := util.SupportAPIResources(MustGetK8sClient().GetDiscoveryClient(), RayApiVersion, RayServiceKind); support {
			rc.enabledRay.Store(true)
		} else {
			rc.enabledRay.Store(false)
		}
	})

	return rc.enabledRay.Load()
}

func (rc *RayClient) GetRayJobInformer() cache.SharedIndexInformer {
	return rc.rayJobInformer
}

func (rc *RayClient) GetRayClusterInformer() cache.SharedIndexInformer {
	return rc.rayClusterInformer
}

func (rc *RayClient) GetRayServiceInformer() cache.SharedIndexInformer {
	return rc.rayServiceInformer
}

func (rc *RayClient) AddRayJobEventHandler(handler cache.ResourceEventHandler) {
	rc.rayJobInformer.AddEventHandler(handler)
	rc.eventHandlers["rayjob"] = append(rc.eventHandlers["rayjob"], handler)
}

func (rc *RayClient) AddRayClusterEventHandler(handler cache.ResourceEventHandler) {
	rc.rayClusterInformer.AddEventHandler(handler)
	rc.eventHandlers["raycluster"] = append(rc.eventHandlers["raycluster"], handler)
}

func (rc *RayClient) AddRayServiceEventHandler(handler cache.ResourceEventHandler) {
	rc.rayServiceInformer.AddEventHandler(handler)
	rc.eventHandlers["rayservice"] = append(rc.eventHandlers["rayservice"], handler)
}

// RayJob methods
func (rc *RayClient) ListRayJobs(namespace string) ([]*rayv1.RayJob, error) {
	if namespace == "" {
		return rc.rayJobLister.List(labels.Everything())
	}
	return rc.rayJobLister.RayJobs(namespace).List(labels.Everything())
}

func (rc *RayClient) GetRayJob(namespace, name string) (*rayv1.RayJob, error) {
	rayJob, err := rc.rayJobLister.RayJobs(namespace).Get(name)
	if err != nil {
		return nil, err
	}
	rayJob.SetGroupVersionKind(RayJobGVK)
	return rayJob, nil
}

func (rc *RayClient) ListRayJobsBySelector(namespace string, selector labels.Selector) ([]*rayv1.RayJob, error) {
	if namespace == "" {
		return rc.rayJobLister.List(selector)
	}
	return rc.rayJobLister.RayJobs(namespace).List(selector)
}

func (rc *RayClient) CreateRayJob(ctx context.Context, rayJob *rayv1.RayJob) (*rayv1.RayJob, error) {
	return rc.clientset.RayV1().RayJobs(rayJob.Namespace).Create(ctx, rayJob, metav1.CreateOptions{})
}

func (rc *RayClient) UpdateRayJob(ctx context.Context, rayJob *rayv1.RayJob) (*rayv1.RayJob, error) {
	return rc.clientset.RayV1().RayJobs(rayJob.Namespace).Update(ctx, rayJob, metav1.UpdateOptions{})
}

func (rc *RayClient) DeleteRayJob(ctx context.Context, namespace, name string) error {
	return rc.clientset.RayV1().RayJobs(namespace).Delete(ctx, name, metav1.DeleteOptions{})
}

// RayCluster methods
func (rc *RayClient) ListRayClusters(namespace string) ([]*rayv1.RayCluster, error) {
	if namespace == "" {
		return rc.rayClusterLister.List(labels.Everything())
	}
	return rc.rayClusterLister.RayClusters(namespace).List(labels.Everything())
}

func (rc *RayClient) GetRayCluster(namespace, name string) (*rayv1.RayCluster, error) {
	rayCluster, err := rc.rayClusterLister.RayClusters(namespace).Get(name)
	if err != nil {
		return nil, err
	}
	rayCluster.SetGroupVersionKind(RayClusterGVK)
	return rayCluster, nil
}

func (rc *RayClient) ListRayClustersBySelector(namespace string, selector labels.Selector) ([]*rayv1.RayCluster, error) {
	if namespace == "" {
		return rc.rayClusterLister.List(selector)
	}
	return rc.rayClusterLister.RayClusters(namespace).List(selector)
}

func (rc *RayClient) CreateRayCluster(ctx context.Context, rayCluster *rayv1.RayCluster) (*rayv1.RayCluster, error) {
	return rc.clientset.RayV1().RayClusters(rayCluster.Namespace).Create(ctx, rayCluster, metav1.CreateOptions{})
}

func (rc *RayClient) UpdateRayCluster(ctx context.Context, rayCluster *rayv1.RayCluster) (*rayv1.RayCluster, error) {
	return rc.clientset.RayV1().RayClusters(rayCluster.Namespace).Update(ctx, rayCluster, metav1.UpdateOptions{})
}

func (rc *RayClient) DeleteRayCluster(ctx context.Context, namespace, name string) error {
	return rc.clientset.RayV1().RayClusters(namespace).Delete(ctx, name, metav1.DeleteOptions{})
}

// RayService methods
func (rc *RayClient) ListRayServices(namespace string) ([]*rayv1.RayService, error) {
	if namespace == "" {
		return rc.rayServiceLister.List(labels.Everything())
	}
	return rc.rayServiceLister.RayServices(namespace).List(labels.Everything())
}

func (rc *RayClient) GetRayService(namespace, name string) (*rayv1.RayService, error) {
	rayService, err := rc.rayServiceLister.RayServices(namespace).Get(name)
	if err != nil {
		return nil, err
	}
	rayService.SetGroupVersionKind(RayServiceGVK)
	return rayService, nil
}

func (rc *RayClient) ListRayServicesBySelector(namespace string, selector labels.Selector) ([]*rayv1.RayService, error) {
	if namespace == "" {
		return rc.rayServiceLister.List(selector)
	}
	return rc.rayServiceLister.RayServices(namespace).List(selector)
}

func (rc *RayClient) CreateRayService(ctx context.Context, rayService *rayv1.RayService) (*rayv1.RayService, error) {
	return rc.clientset.RayV1().RayServices(rayService.Namespace).Create(ctx, rayService, metav1.CreateOptions{})
}

func (rc *RayClient) UpdateRayService(ctx context.Context, rayService *rayv1.RayService) (*rayv1.RayService, error) {
	return rc.clientset.RayV1().RayServices(rayService.Namespace).Update(ctx, rayService, metav1.UpdateOptions{})
}

func (rc *RayClient) DeleteRayService(ctx context.Context, namespace, name string) error {
	return rc.clientset.RayV1().RayServices(namespace).Delete(ctx, name, metav1.DeleteOptions{})
}

func (rc *RayClient) Stop() {
	close(rc.stopCh)
}
