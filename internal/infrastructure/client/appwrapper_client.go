package client

import (
	"context"
	"fmt"
	"sync"
	"sync/atomic"

	appwrapperv1beta2 "github.com/project-codeflare/appwrapper/api/v1beta2"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/watch"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/tools/cache"

	"transwarp.io/mlops/pipeline/internal/util"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

const (
	AppWrapperClientName = "appwrapper"

	AppWrapperApiVersion = "workload.codeflare.dev/v1beta2"
	AppWrapperKind       = "AppWrapper"
)

var (
	AppWrapperGVK = appwrapperv1beta2.GroupVersion.WithKind("AppWrapper")
	AppWrapperGVR = util.InferGVRFromGVK(AppWrapperGVK)

	appWrapperClientInstance *AppWrapperClient
	appWrapperOnce           sync.Once
)

type AppWrapperClient struct {
	client   dynamic.Interface
	informer cache.SharedIndexInformer
	lister   *AppWrapperLister

	enabledAppWrapper atomic.Bool
	once              sync.Once
}

type AppWrapperLister struct {
	lister cache.GenericLister
}

func MustGetAppWrapperClient() *AppWrapperClient {
	client, err := GetAppWrapperClient()
	if err != nil {
		panic(fmt.Sprintf("failed to get AppWrapper client: %v", err))
	}
	return client
}

func GetAppWrapperClient() (*AppWrapperClient, error) {
	var initErr error
	appWrapperOnce.Do(func() {
		config, err := util.GetKubeConfig()
		if err != nil {
			zlog.SugarErrorf("Error building appwrapper client config: %v", err)
			initErr = err
			return
		}

		dynamicClient, err := dynamic.NewForConfig(config)
		if err != nil {
			zlog.SugarErrorf("failed to create dynamic client: %v", err)
			initErr = fmt.Errorf("failed to create dynamic client: %w", err)
			return
		}

		lw := &cache.ListWatch{
			ListFunc: func(options_v1 metav1.ListOptions) (runtime.Object, error) {
				return dynamicClient.Resource(AppWrapperGVR).List(context.TODO(), options_v1)
			},
			WatchFunc: func(options_v1 metav1.ListOptions) (watch.Interface, error) {
				return dynamicClient.Resource(AppWrapperGVR).Watch(context.TODO(), options_v1)
			},
		}

		informer := cache.NewSharedIndexInformer(
			lw,
			&unstructured.Unstructured{},
			// &appwrapperv1beta2.AppWrapper{},
			DefaultResyncPeriod, // Resync period, set to 0 to disable periodic resync.
			cache.Indexers{},
		)

		lister := &AppWrapperLister{
			lister: cache.NewGenericLister(informer.GetIndexer(), AppWrapperGVR.GroupResource()),
		}

		appWrapperClientInstance = &AppWrapperClient{
			client:   dynamicClient,
			informer: informer,
			lister:   lister,
		}

		if appWrapperClientInstance.IsAppWrapperSupported() {
			stopCh := make(chan struct{})
			appWrapperClientInstance.run(stopCh)
		}
	})

	return appWrapperClientInstance, initErr
}

func (c *AppWrapperClient) run(stopCh <-chan struct{}) {
	zlog.SugarInfoln("Starting AppWrapper informer...")
	go c.informer.Run(stopCh)

	if !cache.WaitForCacheSync(stopCh, c.informer.HasSynced) {
		zlog.SugarErrorf("Timed out waiting for AppWrapper caches to sync")
	} else {
		zlog.SugarInfoln("AppWrapper informer caches synced successfully.")
	}
}

func (c *AppWrapperClient) IsAppWrapperSupported() bool {
	c.once.Do(func() {
		if support := util.SupportAPIResources(MustGetK8sClient().GetDiscoveryClient(), AppWrapperApiVersion, AppWrapperKind); support {
			c.enabledAppWrapper.Store(true)
		} else {
			c.enabledAppWrapper.Store(false)
		}
	})

	return c.enabledAppWrapper.Load()
}

func (c *AppWrapperClient) GetAppWrapperInformer() cache.SharedIndexInformer {
	return c.informer
}

func (c *AppWrapperClient) GetAppWrapper(namespace, name string) (*appwrapperv1beta2.AppWrapper, error) {
	obj, err := c.lister.lister.ByNamespace(namespace).Get(name)
	if err != nil {
		return nil, err
	}

	unstructuredObj, ok := obj.(*unstructured.Unstructured)
	if !ok {
		return nil, fmt.Errorf("unexpected object type from lister: %T", obj)
	}

	appwrapper := &appwrapperv1beta2.AppWrapper{}
	if err := runtime.DefaultUnstructuredConverter.FromUnstructured(unstructuredObj.UnstructuredContent(), &appwrapper); err != nil {
		return nil, fmt.Errorf("failed to convert unstructured to AppWrapper: %w", err)
	}

	appwrapper.SetGroupVersionKind(AppWrapperGVK)

	return appwrapper, nil
}

func (c *AppWrapperClient) List(namespace string) ([]*appwrapperv1beta2.AppWrapper, error) {
	var results []*appwrapperv1beta2.AppWrapper
	objs, err := c.lister.lister.ByNamespace(namespace).List(labels.Everything())
	if err != nil {
		return nil, err
	}
	for _, obj := range objs {
		unstructuredObj, ok := obj.(*unstructured.Unstructured)
		if !ok {
			return nil, fmt.Errorf("unexpected object type from lister: %T", obj)
		}

		appwrapper := &appwrapperv1beta2.AppWrapper{}
		if err := runtime.DefaultUnstructuredConverter.FromUnstructured(unstructuredObj.UnstructuredContent(), &appwrapper); err != nil {
			return nil, fmt.Errorf("failed to convert unstructured to AppWrapper: %w", err)
		}
		results = append(results, appwrapper)
	}
	return results, nil
}
