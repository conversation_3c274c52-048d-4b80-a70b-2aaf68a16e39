package client

import (
	"context"
	"fmt"
	"sync"
	"sync/atomic"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/tools/cache"
	kueuev1beta1 "sigs.k8s.io/kueue/apis/kueue/v1beta1"
	kueueclientset "sigs.k8s.io/kueue/client-go/clientset/versioned"
	kueueinformers "sigs.k8s.io/kueue/client-go/informers/externalversions"
	kueuelisters "sigs.k8s.io/kueue/client-go/listers/kueue/v1beta1"

	util "transwarp.io/mlops/pipeline/internal/util"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

const (
	KueueClientName   = "kueue"
	KueueApiVersion   = "kueue.x-k8s.io/v1beta1"
	KueueWorkLoadKind = "Workload"
)

var (
	kueueClientInstance *KueueClient

	WorkloadGVK = kueuev1beta1.SchemeGroupVersion.WithKind("Workload")

	kueueOnce sync.Once
)

type KueueClient struct {
	clientset       kueueclientset.Interface
	informerFactory kueueinformers.SharedInformerFactory

	// Listers
	workloadLister       kueuelisters.WorkloadLister
	localQueueLister     kueuelisters.LocalQueueLister
	clusterQueueLister   kueuelisters.ClusterQueueLister
	resourceFlavorLister kueuelisters.ResourceFlavorLister

	// Informers
	workloadInformer       cache.SharedIndexInformer
	localQueueInformer     cache.SharedIndexInformer
	clusterQueueInformer   cache.SharedIndexInformer
	resourceFlavorInformer cache.SharedIndexInformer

	eventHandlers map[string][]cache.ResourceEventHandler

	stopCh chan struct{}

	enabledKueue atomic.Bool
	once         sync.Once
}

func MustGetKueueClient() *KueueClient {
	client, err := GetKueueClient()
	if err != nil {
		panic(fmt.Sprintf("failed to get Kueue client: %v", err))
	}
	return client
}

func GetKueueClient() (*KueueClient, error) {
	var initErr error
	kueueOnce.Do(func() {
		config, err := util.GetKubeConfig()
		if err != nil {
			zlog.SugarErrorf("Error building kueue client config: %v", err)
			initErr = err
			return
		}

		clientset := kueueclientset.NewForConfigOrDie(config)
		kueueClientInstance = &KueueClient{
			clientset:       clientset,
			informerFactory: kueueinformers.NewSharedInformerFactory(clientset, DefaultResyncPeriod),
			eventHandlers:   make(map[string][]cache.ResourceEventHandler),
			stopCh:          make(chan struct{}),
		}

		if kueueClientInstance.IsKueueSupported() {
			if err := kueueClientInstance.setupAndSyncInformers(context.Background()); err != nil {
				zlog.SugarWarnf("setup kueue informer failed: %v", err)
			}
		}
	})
	return kueueClientInstance, initErr
}

func (c *KueueClient) IsKueueSupported() bool {
	c.once.Do(func() {
		if support := util.SupportAPIResources(MustGetK8sClient().GetDiscoveryClient(), KueueApiVersion, KueueWorkLoadKind); support {
			c.enabledKueue.Store(true)
		} else {
			c.enabledKueue.Store(false)
		}
	})

	return c.enabledKueue.Load()
}

func (kc *KueueClient) WaitForCacheSync(ctx context.Context) bool {
	return cache.WaitForCacheSync(ctx.Done(),
		kc.workloadInformer.HasSynced,
		kc.localQueueInformer.HasSynced,
		kc.clusterQueueInformer.HasSynced,
		kc.resourceFlavorInformer.HasSynced,
	)
}

func (kc *KueueClient) setupAndSyncInformers(ctx context.Context) error {
	if kc.informerFactory == nil {
		zlog.SugarErrorf("Informer factory not initialized, skipping informer setup")
		return fmt.Errorf("informer factory not initialized")
	}

	// Workload Informer
	kc.workloadInformer = kc.informerFactory.Kueue().V1beta1().Workloads().Informer()
	kc.workloadLister = kc.informerFactory.Kueue().V1beta1().Workloads().Lister()

	// LocalQueue Informer
	kc.localQueueInformer = kc.informerFactory.Kueue().V1beta1().LocalQueues().Informer()
	kc.localQueueLister = kc.informerFactory.Kueue().V1beta1().LocalQueues().Lister()

	// ClusterQueue Informer
	kc.clusterQueueInformer = kc.informerFactory.Kueue().V1beta1().ClusterQueues().Informer()
	kc.clusterQueueLister = kc.informerFactory.Kueue().V1beta1().ClusterQueues().Lister()

	// ResourceFlavor Informer
	kc.resourceFlavorInformer = kc.informerFactory.Kueue().V1beta1().ResourceFlavors().Informer()
	kc.resourceFlavorLister = kc.informerFactory.Kueue().V1beta1().ResourceFlavors().Lister()

	go kc.informerFactory.Start(kc.stopCh)

	syncCtx, cancel := context.WithTimeout(ctx, DefaultInformerSyncTimeout) // Use a context derived from the input context
	defer cancel()

	if !kc.WaitForCacheSync(syncCtx) {
		return fmt.Errorf("timed out waiting for Kueue cache sync. Kueue may not be installed or reachable")
	}

	zlog.SugarInfoln("Kueue informers cache synced successfully.")
	return nil
}

func (kc *KueueClient) AddWorkloadEventHandler(handler cache.ResourceEventHandler) {
	kc.workloadInformer.AddEventHandler(handler)
	kc.eventHandlers["workload"] = append(kc.eventHandlers["workload"], handler)
}

func (kc *KueueClient) AddLocalQueueEventHandler(handler cache.ResourceEventHandler) {
	kc.localQueueInformer.AddEventHandler(handler)
	kc.eventHandlers["localqueue"] = append(kc.eventHandlers["localqueue"], handler)
}

func (kc *KueueClient) AddClusterQueueEventHandler(handler cache.ResourceEventHandler) {
	kc.clusterQueueInformer.AddEventHandler(handler)
	kc.eventHandlers["clusterqueue"] = append(kc.eventHandlers["clusterqueue"], handler)
}

func (kc *KueueClient) ListWorkloads(namespace string) ([]*kueuev1beta1.Workload, error) {
	if namespace == "" {
		return kc.workloadLister.List(labels.Everything())
	}
	workloads, err := kc.workloadLister.Workloads(namespace).List(labels.Everything())
	if err != nil {
		return nil, err
	}
	for _, wl := range workloads {
		wl.SetGroupVersionKind(WorkloadGVK)
	}
	return workloads, nil
}

func (kc *KueueClient) GetWorkload(namespace, name string) (*kueuev1beta1.Workload, error) {
	wl, err := kc.workloadLister.Workloads(namespace).Get(name)
	if err != nil {
		return nil, err
	}
	wl.SetGroupVersionKind(WorkloadGVK)
	return wl, nil
}

func (kc *KueueClient) ListWorkloadsBySelector(namespace string, selector labels.Selector) ([]*kueuev1beta1.Workload, error) {
	if namespace == "" {
		return kc.workloadLister.List(selector)
	}
	return kc.workloadLister.Workloads(namespace).List(selector)
}

func (kc *KueueClient) CreateWorkload(ctx context.Context, workload *kueuev1beta1.Workload) (*kueuev1beta1.Workload, error) {
	return kc.clientset.KueueV1beta1().Workloads(workload.Namespace).Create(ctx, workload, metav1.CreateOptions{})
}

func (kc *KueueClient) UpdateWorkload(ctx context.Context, workload *kueuev1beta1.Workload) (*kueuev1beta1.Workload, error) {
	return kc.clientset.KueueV1beta1().Workloads(workload.Namespace).Update(ctx, workload, metav1.UpdateOptions{})
}

func (kc *KueueClient) DeleteWorkload(ctx context.Context, namespace, name string) error {
	return kc.clientset.KueueV1beta1().Workloads(namespace).Delete(ctx, name, metav1.DeleteOptions{})
}

func (kc *KueueClient) ListLocalQueues(namespace string) ([]*kueuev1beta1.LocalQueue, error) {
	if namespace == "" {
		return kc.localQueueLister.List(labels.Everything())
	}
	return kc.localQueueLister.LocalQueues(namespace).List(labels.Everything())
}

func (kc *KueueClient) GetLocalQueue(namespace, name string) (*kueuev1beta1.LocalQueue, error) {
	return kc.localQueueLister.LocalQueues(namespace).Get(name)
}

func (kc *KueueClient) ListClusterQueues() ([]*kueuev1beta1.ClusterQueue, error) {
	return kc.clusterQueueLister.List(labels.Everything())
}

func (kc *KueueClient) GetClusterQueue(name string) (*kueuev1beta1.ClusterQueue, error) {
	return kc.clusterQueueLister.Get(name)
}

func (kc *KueueClient) ListResourceFlavors() ([]*kueuev1beta1.ResourceFlavor, error) {
	return kc.resourceFlavorLister.List(labels.Everything())
}

func (kc *KueueClient) GetResourceFlavor(name string) (*kueuev1beta1.ResourceFlavor, error) {
	return kc.resourceFlavorLister.Get(name)
}
