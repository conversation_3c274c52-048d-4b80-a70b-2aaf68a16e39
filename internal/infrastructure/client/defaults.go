package client

import (
	"context"
	"sync"
	"time"

	"google.golang.org/grpc/metadata"
	token "transwarp.io/mlops/mlops-std/token"
)

const (
	DefaultResyncPeriod        = 15 * time.Minute
	DefaultInformerSyncTimeout = 60 * time.Second
)

var defaultContext = sync.OnceValues(func() (context.Context, error) {
	ctx := context.Background()
	info := &token.TokenInfo{}
	info.UserName = "thinger"
	tokenStr := token.Encode(info)
	md := metadata.New(map[string]string{token.AUTH_HEADER: tokenStr})
	return metadata.NewIncomingContext(ctx, md), nil
})
