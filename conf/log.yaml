logging:
  level: "info" #DEBUG (-1), INFO (0), WARN (1), ERROR (2), DPANIC (3), PANIC (4), FATAL (5)
  appenders:
    - name: "stdout"
      type: "stdout"
      encoder: "console"
      filter:
        min_level: "info"
        max_level: "fatal"

    # - name: "file-info"
    #   type: "file"
    #   path: "/var/log/yushu/info.log"
    #   encoder: "json"
    #   policy:
    #     type: "size"
    #     max_size: 100MB
    #     max_backups: 14
    #     max_age: 14d
    #   filter:
    #     min_level: "info"
    #     max_level: "info"

    # - name: "file-error"
    #   type: "file"
    #   path: "/var/log/yushu/error.log"
    #   encoder: "json"
    #   policy:
    #     type: "time"
    #     pattern: "%Y-%m-%d-%H-%M-%S"
    #     interval: 24h
    #     max_age: 14d
    #   filter:
    #     min_level: "error"

  loggers:
    appender_refs: ["stdout"]
    #appender_refs: ["stdout", "file-info", "file-error"]