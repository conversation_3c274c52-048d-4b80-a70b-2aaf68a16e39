mlops:
  logConfig:
    path: /var/log/sophon/pipeline-server.log
    level: info # default:"info" validate:"regexp=^(info|debug|warn|error)$"`
    format: text # default:"text" validate:"regexp=^(text|json)$"`
    console: true # default:"false"`
    Age:
      Max: 15 # default:"15" validate:"min=1"`
    Size:
      Max: 50 # default:"50" validate:"min=1"`
    Backup:
      Max: 15 # default:"15" validate:"min=0"`
  datasource:
    driver: mysql
    username: root
    password: Warp!CV@2022#
    host: "autocv-mysql-service"
    port: "3306"
    dbname: metastore_pipeline_llmops
    maxidle: 5
    maxconn: 10
    notprintsql: false
    notcreatetable: false
  mlpipelineDatasource:
    driver: mysql
    username: root
    password: Warp!CV@2022#
    host: "autocv-mysql-service"
    port: "3306"
    dbname: mlpipeline
    maxidle: 5
    maxconn: 10
    notprintsql: false
    notcreatetable: false
  httpServer:
    Port: 8761
    Name: pipeline
  grpcServer:
    Port: 8762
  serviceconfig:
    InstanceId: sophon_mlops
    ImageTag: dev
    DockerImageRepo: harbor.transwarp.io/aip
    SchedulerName: default-scheduler
  etcd:
    endpoints: http://llmops-sophon-etcd.llmops:2479
    dialTimeout: 5
  pipeline:
    namespace: kubeflow
    host: ml-pipeline.kubeflow.svc
    port: 8887
  serving:
    grpcServer:
      Host: llmops-sophon-serving.llmops
      Port: 8752
  warehouse:
    minio:
      endpoint: autocv-ossgw-service:30900
      accessKey: root
      secretKey: Warp!CV@2022#
  storage:
    sfs:
      pvcName: sfs-pvc
pipeline:
  redis:
    addrs: "autocv-redis-service.llmops.svc.cluster.local:6379"
    database: "0"
    username:
    password: root