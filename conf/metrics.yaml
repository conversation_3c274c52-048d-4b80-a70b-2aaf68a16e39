prometheus:
  endpoint: "http://*************:30090"
  #endpoint: "http://*************:31601"
  username: ""
  password: ""
  timeout: 1s
  max_retries: 1
  retry_delay: 1s
  enable_tls: false
  tls_config:
    cert_file: ""
    key_file: ""
    ca_file: ""
    insecure_skip_verify: false

cache:
  enabled: true
  ttl: 5m
  max_size: 30000
  interval: 30s
  cleanup_interval: 10m

metrics:
  pod_cpu_usage:
    name: "pod_cpu_usage"
    query: |
      sum(rate(llmops_container_cpu_usage_seconds_total{ {{- if .Namespace}}ref_pod_namespace="{{.Namespace}}"{{end}} {{- if .PodName}},ref_pod_name="{{.PodName}}"{{end}} {{- if .Labels}}{{range $k, $v := .Labels}},{{$k}}="{{$v}}"{{end}}{{end}} }[{{default "5m" .TimeRange}}])) by (ref_pod_name, ref_pod_namespace) * 1000
    description: "Pod CPU usage"
    unit: "m"
    type: "counter"
    labels: ["ref_pod_name", "ref_pod_namespace", "node"]
    interval: 30s
    timeout: 10s

  pod_memory_usage:
    name: "pod_memory_usage"
    query: |
      sum(llmops_container_memory_working_set_bytes{ {{if .Namespace}}ref_pod_namespace="{{.Namespace}}"{{end}} {{if .PodName}},ref_pod_name="{{.PodName}}"{{end}} {{if .Labels}}{{range $k, $v := .Labels}},{{$k}}="{{$v}}"{{end}}{{end}}} ) by (ref_pod_name, ref_pod_namespace) / 1024 / 1024
    description: "Pod memory usage MiB"
    unit: "MiB"
    type: "gauge"
    labels: ["ref_pod_name", "ref_pod_namespace", "node"]
    interval: 30s
    timeout: 10s

  pod_gpu_usage:
    name: "pod_gpu_usage"
    query: |
      sum(llmops_hami_container_core_used{ {{- if .Namespace}}ref_pod_namespace="{{.Namespace}}"{{end}} {{- if .PodName}},ref_pod_name="{{.PodName}}"{{end}} {{- if .Labels}}{{range $k, $v := .Labels}},{{$k}}="{{$v}}"{{end}}{{end}} }) by (ref_pod_name, ref_pod_namespace,devicetype)
    description: "Pod GPU usage"
    unit: "cores"
    type: "gauge"
    labels: ["ref_pod_name", "ref_pod_namespace", "node"]
    interval: 30s
    timeout: 10s

  pod_gpu_memory_usage:
    name: "pod_gpu_memory_usage"
    query: |
      sum(llmops_hami_container_memory_used{ {{if .Namespace}}ref_pod_namespace="{{.Namespace}}"{{end}} {{if .PodName}},ref_pod_name="{{.PodName}}"{{end}} {{if .Labels}}{{range $k, $v := .Labels}},{{$k}}="{{$v}}"{{end}}{{end}}} ) by (ref_pod_name, ref_pod_namespace,devicetype)
    description: "Pod GPU memory usage MiB"
    unit: "MiB"
    type: "gauge"
    labels: ["ref_pod_name", "ref_pod_namespace", "node"]
    interval: 30s
    timeout: 10s